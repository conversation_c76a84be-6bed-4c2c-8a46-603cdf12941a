package org.example.kafka

import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.common.serialization.StringDeserializer
import java.time.Duration
import java.util.Properties

fun main() {
    val props = Properties().apply {
        put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.20.129.16:9092,172.20.129.16:9092,172.20.129.16:9092") // 改为实际地址
        put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer::class.java)
        put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer::class.java)
        put(ConsumerConfig.GROUP_ID_CONFIG, "kotlin-ais-consumer-group") // 消费者组
        put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest") // 可选 "earliest" | "latest"

        // 安全协议
        put("security.protocol", "SASL_PLAINTEXT") // 或 SASL_SSL（如有加密）
        put("sasl.mechanism", "PLAIN") // 或 SCRAM-SHA-256、SCRAM-SHA-512

        // SASL 配置：用户名和密码
        put(
            "sasl.jaas.config", """
            org.apache.kafka.common.security.plain.PlainLoginModule required
            username="consumer3"
            password="consumer33";
        """.trimIndent()
        )
    }

    val consumer = KafkaConsumer<String, String>(props)
    consumer.subscribe(listOf("dpp_hsj_ais_data_down"))

    println("🚀 正在监听 Kafka 话题：dpp_hsj_ais_data_down")
    while (true) {
        val records = consumer.poll(Duration.ofMillis(1000))
        for (record in records) {
            println("offset=${record.offset()}, key=${record.key()}, value=${record.value()}")
        }
    }
}