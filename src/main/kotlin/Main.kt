package org.example

import io.ktor.server.application.Application
import io.ktor.server.engine.embeddedServer
import io.ktor.server.netty.Netty
import io.ktor.server.request.receiveText
import io.ktor.server.response.respondText
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import kotlinx.coroutines.launch
import org.example.datasource.DataSourceManager.dataChannel
import org.example.datasource.msa.hainan.HNTask
import org.example.util.Sm4Util.registerBouncyCastleProvider
import kotlin.math.PI


fun main() {
//    embeddedServer(Netty, port = 11115, host = "0.0.0.0", module = Application::module)
//        .start(wait = true)
//    Port.queryPosition(3)
//    Port.runConcurrentQuery(3)
//    Port.queryDistinctShips()
//    Port.runConcurrentQuery(5)
}

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
fun Application.module() {
    registerBouncyCastleProvider()
//    launch { startNettyTcpServer() }
//    launch { startTaskProcessor(this) }
//    launch { GDTask.launchTask() }
//    launch { GXTask.launchTask() }
    launch { HNTask.launchTask() }

    routing {
        post("/collect") {
            val body = call.receiveText()
            dataChannel.trySend("HTTP: $body")
            call.respondText("HTTP Data received")
        }
    }
}