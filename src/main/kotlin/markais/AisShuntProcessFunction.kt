package org.example.markais

import org.apache.flink.streaming.api.functions.ProcessFunction
import org.apache.flink.util.Collector
import org.example.markais.AisOutputTag.AIS_MESSAGE_1
import org.example.markais.AisOutputTag.AIS_MESSAGE_2
import org.example.markais.AisOutputTag.AIS_MESSAGE_3
import org.example.markais.AisOutputTag.AIS_MESSAGE_4
import org.example.markais.AisOutputTag.AIS_MESSAGE_5
import org.example.markais.AisOutputTag.AIS_MESSAGE_6
import org.example.markais.AisOutputTag.AIS_MESSAGE_7
import org.example.markais.AisOutputTag.AIS_MESSAGE_8
import org.example.markais.AisOutputTag.AIS_MESSAGE_9
import org.example.markais.AisOutputTag.AIS_MESSAGE_10
import org.example.markais.AisOutputTag.AIS_MESSAGE_11
import org.example.markais.AisOutputTag.AIS_MESSAGE_12
import org.example.markais.AisOutputTag.AIS_MESSAGE_13
import org.example.markais.AisOutputTag.AIS_MESSAGE_14
import org.example.markais.AisOutputTag.AIS_MESSAGE_15
import org.example.markais.AisOutputTag.AIS_MESSAGE_16
import org.example.markais.AisOutputTag.AIS_MESSAGE_17
import org.example.markais.AisOutputTag.AIS_MESSAGE_18
import org.example.markais.AisOutputTag.AIS_MESSAGE_19
import org.example.markais.AisOutputTag.AIS_MESSAGE_20
import org.example.markais.AisOutputTag.AIS_MESSAGE_21
import org.example.markais.AisOutputTag.AIS_MESSAGE_22
import org.example.markais.AisOutputTag.AIS_MESSAGE_23
import org.example.markais.AisOutputTag.AIS_MESSAGE_24
import org.example.markais.AisOutputTag.AIS_MESSAGE_25
import org.example.markais.AisOutputTag.AIS_MESSAGE_26
import org.example.markais.AisOutputTag.AIS_MESSAGE_27
import org.example.model.ais.AisMessage
import org.example.model.ais.AisMessage1
import org.example.model.ais.AisMessage2
import org.example.model.ais.AisMessage3
import org.example.model.ais.AisMessage4
import org.example.model.ais.AisMessage5
import org.example.model.ais.AisMessage6
import org.example.model.ais.AisMessage7
import org.example.model.ais.AisMessage8
import org.example.model.ais.AisMessage9
import org.example.model.ais.AisMessage10
import org.example.model.ais.AisMessage11
import org.example.model.ais.AisMessage12
import org.example.model.ais.AisMessage13
import org.example.model.ais.AisMessage14
import org.example.model.ais.AisMessage15
import org.example.model.ais.AisMessage16
import org.example.model.ais.AisMessage17
import org.example.model.ais.AisMessage18
import org.example.model.ais.AisMessage19
import org.example.model.ais.AisMessage20
import org.example.model.ais.AisMessage21
import org.example.model.ais.AisMessage22
import org.example.model.ais.AisMessage23
import org.example.model.ais.AisMessage24
import org.example.model.ais.AisMessage25
import org.example.model.ais.AisMessage26
import org.example.model.ais.AisMessage27
import org.example.util.CommonUtil.log

class AisShuntProcessFunction : ProcessFunction<AisMessage, String>() {
    override fun processElement(
        value: AisMessage,
        ctx: ProcessFunction<AisMessage, String>.Context,
        out: Collector<String>
    ) {
        when (value.msgId) {
            1 -> ctx.output(AIS_MESSAGE_1, value as AisMessage1)
            2 -> ctx.output(AIS_MESSAGE_2, value as AisMessage2)
            3 -> ctx.output(AIS_MESSAGE_3, value as AisMessage3)
            4 -> ctx.output(AIS_MESSAGE_4, value as AisMessage4)
            5 -> ctx.output(AIS_MESSAGE_5, value as AisMessage5)
            6 -> ctx.output(AIS_MESSAGE_6, value as AisMessage6)
            7 -> ctx.output(AIS_MESSAGE_7, value as AisMessage7)
            8 -> ctx.output(AIS_MESSAGE_8, value as AisMessage8)
            9 -> ctx.output(AIS_MESSAGE_9, value as AisMessage9)
            10 -> ctx.output(AIS_MESSAGE_10, value as AisMessage10)
            11 -> ctx.output(AIS_MESSAGE_11, value as AisMessage11)
            12 -> ctx.output(AIS_MESSAGE_12, value as AisMessage12)
            13 -> ctx.output(AIS_MESSAGE_13, value as AisMessage13)
            14 -> ctx.output(AIS_MESSAGE_14, value as AisMessage14)
            15 -> ctx.output(AIS_MESSAGE_15, value as AisMessage15)
            16 -> ctx.output(AIS_MESSAGE_16, value as AisMessage16)
            17 -> ctx.output(AIS_MESSAGE_17, value as AisMessage17)
            18 -> ctx.output(AIS_MESSAGE_18, value as AisMessage18)
            19 -> ctx.output(AIS_MESSAGE_19, value as AisMessage19)
            20 -> ctx.output(AIS_MESSAGE_20, value as AisMessage20)
            21 -> ctx.output(AIS_MESSAGE_21, value as AisMessage21)
            22 -> ctx.output(AIS_MESSAGE_22, value as AisMessage22)
            23 -> ctx.output(AIS_MESSAGE_23, value as AisMessage23)
            24 -> ctx.output(AIS_MESSAGE_24, value as AisMessage24)
            25 -> ctx.output(AIS_MESSAGE_25, value as AisMessage25)
            26 -> ctx.output(AIS_MESSAGE_26, value as AisMessage26)
            27 -> ctx.output(AIS_MESSAGE_27, value as AisMessage27)
            else -> log.error("Unsupported AIS message id: ${value.msgId}")
        }
    }
}