package org.example.markais

import org.apache.flink.api.common.state.MapState
import org.apache.flink.api.common.state.MapStateDescriptor
import org.apache.flink.api.common.state.ValueState
import org.apache.flink.api.common.state.ValueStateDescriptor
import org.apache.flink.api.common.typeinfo.Types
import org.apache.flink.configuration.Configuration
import org.apache.flink.streaming.api.functions.KeyedProcessFunction
import org.apache.flink.util.Collector
import org.example.model.ais.AisMessage
import org.example.model.ais.NMEA
import org.example.util.MessageUtil.charToSixBit
import org.example.util.SixBitWriter

class AisReassembleFunction : KeyedProcessFunction<String, NMEA, AisMessage>() {
    private lateinit var bufferState: MapState<Int, NMEA>
    private lateinit var firstArrivalTimeState: ValueState<Long>
    private lateinit var fastReader: SixBitWriter

    override fun open(parameters: Configuration?) {
        bufferState = runtimeContext.getMapState(
            MapStateDescriptor("buffer", Types.INT, Types.POJO(NMEA::class.java))
        )
        firstArrivalTimeState = runtimeContext.getState(
            ValueStateDescriptor("firstArrivalTime", Types.LONG)
        )
        fastReader = SixBitWriter(2048)
    }

    override fun processElement(
        value: NMEA,
        ctx: Context,
        out: Collector<AisMessage>
    ) {
        val segmentNum: Int = value.currentSegment.toIntOrNull() ?: return
        val totalSegments = value.totalSegments.toIntOrNull() ?: return

        // 设置第一次到达时间
        if (firstArrivalTimeState.value() == null) {
            firstArrivalTimeState.update(ctx.timerService().currentProcessingTime())
            // 设置一个10秒的超时时间
            ctx.timerService().registerProcessingTimeTimer(
                ctx.timerService().currentProcessingTime() + 10_000
            )
        }

        bufferState.put(segmentNum, value)

        // 检查是否已经接收完整
        if ((1..totalSegments).all { bufferState.contains(it) }) {
            (1..totalSegments).mapNotNull { bufferState[it] }.forEach { nmea ->
                nmea.payload.forEach { fastReader.writeBits(charToSixBit(it)) }
                if (nmea.padBits.isNotEmpty()) {
                    fastReader.writeBits(0, nmea.padBits.toIntOrNull() ?: 0)
                }
            }

            out.collect(AisMessage(fastReader.toByteArray()))

            // 清理状态
            bufferState.clear()
            firstArrivalTimeState.clear()
            fastReader.reset()
        }
    }

    override fun onTimer(timestamp: Long, ctx: OnTimerContext, out: Collector<AisMessage>) {
        // 清理过期的碎片数据
        bufferState.clear()
        firstArrivalTimeState.clear()
        fastReader.reset()
    }
}
