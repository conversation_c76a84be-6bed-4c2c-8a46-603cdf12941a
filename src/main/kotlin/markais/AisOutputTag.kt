package org.example.markais

import org.apache.flink.util.OutputTag
import org.example.model.ais.AisMessage1
import org.example.model.ais.AisMessage2
import org.example.model.ais.AisMessage3
import org.example.model.ais.AisMessage4
import org.example.model.ais.AisMessage5
import org.example.model.ais.AisMessage6
import org.example.model.ais.AisMessage7
import org.example.model.ais.AisMessage8
import org.example.model.ais.AisMessage9
import org.example.model.ais.AisMessage10
import org.example.model.ais.AisMessage11
import org.example.model.ais.AisMessage12
import org.example.model.ais.AisMessage13
import org.example.model.ais.AisMessage14
import org.example.model.ais.AisMessage15
import org.example.model.ais.AisMessage16
import org.example.model.ais.AisMessage17
import org.example.model.ais.AisMessage18
import org.example.model.ais.AisMessage19
import org.example.model.ais.AisMessage20
import org.example.model.ais.AisMessage21
import org.example.model.ais.AisMessage22
import org.example.model.ais.AisMessage23
import org.example.model.ais.AisMessage24
import org.example.model.ais.AisMessage25
import org.example.model.ais.AisMessage26
import org.example.model.ais.AisMessage27

object AisOutputTag {
    val AIS_MESSAGE_1 = object : OutputTag<AisMessage1>("ais-message-1") {}
    val AIS_MESSAGE_2 = object : OutputTag<AisMessage2>("ais-message-2") {}
    val AIS_MESSAGE_3 = object : OutputTag<AisMessage3>("ais-message-3") {}
    val AIS_MESSAGE_4 = object : OutputTag<AisMessage4>("ais-message-4") {}
    val AIS_MESSAGE_5 = object : OutputTag<AisMessage5>("ais-message-5") {}
    val AIS_MESSAGE_6 = object : OutputTag<AisMessage6>("ais-message-6") {}
    val AIS_MESSAGE_7 = object : OutputTag<AisMessage7>("ais-message-7") {}
    val AIS_MESSAGE_8 = object : OutputTag<AisMessage8>("ais-message-8") {}
    val AIS_MESSAGE_9 = object : OutputTag<AisMessage9>("ais-message-9") {}
    val AIS_MESSAGE_10 = object : OutputTag<AisMessage10>("ais-message-10") {}
    val AIS_MESSAGE_11 = object : OutputTag<AisMessage11>("ais-message-11") {}
    val AIS_MESSAGE_12 = object : OutputTag<AisMessage12>("ais-message-12") {}
    val AIS_MESSAGE_13 = object : OutputTag<AisMessage13>("ais-message-13") {}
    val AIS_MESSAGE_14 = object : OutputTag<AisMessage14>("ais-message-14") {}
    val AIS_MESSAGE_15 = object : OutputTag<AisMessage15>("ais-message-15") {}
    val AIS_MESSAGE_16 = object : OutputTag<AisMessage16>("ais-message-16") {}
    val AIS_MESSAGE_17 = object : OutputTag<AisMessage17>("ais-message-17") {}
    val AIS_MESSAGE_18 = object : OutputTag<AisMessage18>("ais-message-18") {}
    val AIS_MESSAGE_19 = object : OutputTag<AisMessage19>("ais-message-19") {}
    val AIS_MESSAGE_20 = object : OutputTag<AisMessage20>("ais-message-20") {}
    val AIS_MESSAGE_21 = object : OutputTag<AisMessage21>("ais-message-21") {}
    val AIS_MESSAGE_22 = object : OutputTag<AisMessage22>("ais-message-22") {}
    val AIS_MESSAGE_23 = object : OutputTag<AisMessage23>("ais-message-23") {}
    val AIS_MESSAGE_24 = object : OutputTag<AisMessage24>("ais-message-24") {}
    val AIS_MESSAGE_25 = object : OutputTag<AisMessage25>("ais-message-25") {}
    val AIS_MESSAGE_26 = object : OutputTag<AisMessage26>("ais-message-26") {}
    val AIS_MESSAGE_27 = object : OutputTag<AisMessage27>("ais-message-27") {}
}