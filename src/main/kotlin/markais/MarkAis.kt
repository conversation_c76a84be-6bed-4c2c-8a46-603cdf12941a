package org.example.markais

import ch.hsr.geohash.GeoHash
import org.apache.flink.api.common.eventtime.WatermarkStrategy
import org.apache.flink.api.common.serialization.SimpleStringEncoder
import org.apache.flink.connector.file.sink.FileSink
import org.apache.flink.core.fs.Path
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.streaming.api.functions.ProcessFunction
import org.apache.flink.util.OutputTag
import org.example.markais.AisOutputTag.AIS_MESSAGE_1
import org.example.markais.AisOutputTag.AIS_MESSAGE_5
import org.example.model.SatellitePositionData
import org.example.model.SatelliteShipData
import org.example.netty.NettyClientConfig
import org.example.netty.NettyConnectionManager
import org.example.netty.NettyTcpSource
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.MessageUtil
import org.example.util.customObjectMapper
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

fun main() {
    val clickhouseAddress = "************:8123"
    val tcpConnections = listOf(
//        NettyClientConfig("*************", 9899, "TjBeiBao", "")
        NettyClientConfig("*************", 8040, "tjhbc", "tjhbc")
    )

    val sink = FileSink
        .forRowFormat(Path("D:\\Project\\shenlan\\test\\netty-demo\\data\\"), SimpleStringEncoder<String>("UTF-8"))
        .build()

    val env = StreamExecutionEnvironment.getExecutionEnvironment()
    val nettySource = NettyTcpSource(tcpConnections)
    val stream = env.fromSource(nettySource, WatermarkStrategy.noWatermarks(), "netty-tcp-source-mark-ais")

    val processedStream = stream.map {
        MessageUtil.creatNMEA(it)
    }.keyBy {
        it.sequenceNumber
    }.process(AisReassembleFunction()).process(AisShuntProcessFunction())

    val stream1 = processedStream.getSideOutput(AIS_MESSAGE_1).map { customObjectMapper.writeValueAsString(it) }
    val stream2 = processedStream.getSideOutput(AIS_MESSAGE_5).map { customObjectMapper.writeValueAsString(it) }

    // 合并两个流
    val mergedStream = stream1.union(stream2)

    // 统一输出到文件
    mergedStream.sinkTo(sink)

    Runtime.getRuntime().addShutdownHook(Thread {
        NettyConnectionManager.shutdown()
    })

    env.execute()
}