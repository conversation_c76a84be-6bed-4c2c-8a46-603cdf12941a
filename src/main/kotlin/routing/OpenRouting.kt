package org.example.routing

import io.ktor.server.application.Application
import io.ktor.server.application.log
import io.ktor.server.request.receive
import io.ktor.server.request.receiveText
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import org.example.datasource.msa.heilongjiang.HLJPositionRequest
import org.example.datasource.msa.heilongjiang.HLJResponse
import java.util.Date

fun Application.configureRouting() {
    routing {
        route("/api/open") {
            post("/pushPosition") {
                try {
                    val body = call.receive<HLJPositionRequest>()
                    log.info("接收黑龙江推送数据大小：${body.data.positions.size}")
                    call.respond(HLJResponse(null, Date().time, "成功", "0"))
                } catch (e: Exception) {
                    call.respond(HLJResponse(null, Date().time, "参数不合法!", "E9993"))
                }
            }

            post("/alarm") {
                call.respond(HLJResponse(null, Date().time, "成功", "0"))
            }
        }
    }
}