package org.example.annotations

@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class Order(val index: Int, val description: String = "")

@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class Bit(val index: Int, val bit: Int = 0, val description: String = "", val nameFlag: Boolean = false, val uniqueId: String  = "")

@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class Indicator(val name: String, val type: Int = 0)