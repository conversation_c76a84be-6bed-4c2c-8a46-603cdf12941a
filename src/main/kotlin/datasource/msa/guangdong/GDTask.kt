package org.example.datasource.msa.guangdong

import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.CommonUtil.log
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest
import java.security.MessageDigest
import java.time.LocalDateTime

object GDTask {
    const val USERNAME = "MSAFish"
    const val PASSWORD = "MSA@2025Fish"
    const val URL = "http://api.gd.msa.gov.cn"
    val endpointMap = mutableMapOf(
        "login" to "/esb/api/proxy/security/token/authorize_token",
        "fetchData" to "/esb2/80/FishInfoBD"
    )
    var token = ""

    fun launchTask() {
        loginScheduledHttpCall(25 * 60 * 1000)
        fetchShipsScheduledHttpCall(5 * 60 * 1000)
    }

    fun loginScheduledHttpCall(intervalMillis: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                try {
                    val md5 = MessageDigest.getInstance("MD5")
                    val hash = md5.digest(PASSWORD.toByteArray()).joinToString("") { "%02x".format(it) }.uppercase()
                    val time = localDateTimeFormatter.format(LocalDateTime.now())
                    val password = md5.digest("$hash$time".toByteArray()).joinToString("") { "%02x".format(it) }.uppercase()
                    log.info("GDTask.loginScheduledHttpCall time: $time, password: $password")

                    val resultText = retryRequest {
                        client.get("$URL${endpointMap.getOrDefault("login", "/esb/api/proxy/security/token/authorize_token")}") {
                            contentType(ContentType.Application.Json)
                            url {
                                parameters.append("username", USERNAME)
                                parameters.append("password", password)
                                parameters.append("ts", time)
                            }
                        }.bodyAsText()
                    }
                    token = Json.decodeFromString<LoginAuthResponse>(resultText).result.token
                    log.info("GDTask.loginScheduledHttpCall token: $token")
                } catch (e: Exception) {
                    log.error("GDTask.loginScheduledHttpCall HTTP call failed: ${e.message}")
                }
                delay(intervalMillis)
            }
        }
    }

    fun fetchShipsScheduledHttpCall(intervalMillis: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                try {
                    val result = retryRequest<List<GDFishingData>> {
                        client.get("$URL${endpointMap.getOrDefault("fetchData", "/esb2/80/FishInfoBD")}") {
                            contentType(ContentType.Application.Json)
                            url {
                                parameters.append("token", token)
                            }
                        }.body()
                    }
                    log.info("GDTask.fetchShipsScheduledHttpCall result: ${result.size}")
                } catch (e: Exception) {
                    log.error("GDTask.fetchShipsScheduledHttpCall HTTP call failed: ${e.message}")
                }
                delay(intervalMillis)
            }
        }
    }
}