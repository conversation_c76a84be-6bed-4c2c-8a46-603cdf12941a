package org.example.datasource.msa.guangdong

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LoginRequest(
    @SerialName("username")
    val username: String,
    @SerialName("password")
    val password: String,
    @SerialName("ts")
    val ts: String
)

@Serializable
data class LoginAuthResponse(
    @SerialName("apiVersion")
    val apiVersion: String,
    @SerialName("code")
    val code: String,
    @SerialName("message")
    val message: String,
    @SerialName("requestId")
    val requestId: String,
    @SerialName("result")
    val result: LoginAuthResult
)

@Serializable
data class LoginAuthResult(
    @SerialName("token")
    val token: String
)

@Serializable
data class GDFishingData(
    @SerialName("BM") val bm: String? = null,
    @SerialName("CALLSIGN") val callSign: String? = null,
    @SerialName("COG") val courseOverGround: Double = 0.0,
    @SerialName("DEST") val dest: String? = null,
    @SerialName("DRAUGHT") val draught: String? = null,
    @SerialName("EPFS") val epfs: String? = null,
    @SerialName("ETA") val eta: String? = null,
    @SerialName("FROMTYPE") val fromType: Int? = null,
    @SerialName("HEADING") val heading: Int? = null,
    @SerialName("ID") val id: String? = null,
    @SerialName("IMO") val imo: String? = null,
    @SerialName("LAT") val lat: Double? = null,
    @SerialName("LNG") val lng: Double? = null,
    @SerialName("LOA") val loa: String? = null,
    @SerialName("MID") val mid: String? = null,
    @SerialName("MMSI") val mmsi: String? = null,
    @SerialName("NAME") val name: String? = null,
    @SerialName("RCVTIME") val rcvTime: String? = null,
    @SerialName("ROT") val rot: String? = null,
    @SerialName("SN") val sn: String? = null,
    @SerialName("SOG") val speedOverGround: Double = 0.0,
    @SerialName("STATE") val state: String? = null,
    @SerialName("TIMESTAMP") val timestamp: Int? = null,
    @SerialName("TYPE") val type: Int? = null,
    @SerialName("UMC") val umc: String? = null
)