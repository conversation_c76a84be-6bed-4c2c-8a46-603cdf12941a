package org.example.datasource.msa.guangxi

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GXResponse(
    val code: Int,
    val message: String,
    val data: GXPageData
)

@Serializable
data class GXPageData(
    val current: Int,
    val pages: Int,
    val size: Int,
    val total: Int,
    val data: List<GXFishingData>
)

@Serializable
data class GXFishingData(
    @SerialName("shipUuid")
    val shipUuid: String = "",
    @SerialName("ownerName")
    val ownerName: String = "",
    @SerialName("distShipDistrictName")
    val distShipDistrictName: String = "",
    @SerialName("nationalityCertNumber")
    val nationalityCertNumber: String = "",
    @SerialName("ownerHolderNo")
    val ownerHolderNo: String = "",
    @SerialName("shipNetTon")
    val shipNetTon: String = "",
    @SerialName("shipPort")
    val shipPort: String = "",
    @SerialName("shipDeep")
    val shipDeep: String = "",
    @SerialName("shipWidth")
    val shipWidth: String = "",
    @SerialName("fishingPermitNumber")
    val fishingPermitNumber: String = "",
    @SerialName("shipCall")
    val shipCall: String = "",
    @SerialName("registerNumber")
    val registerNumber: String = "",
    @SerialName("shipTotPower")
    val shipTotPower: String = "",
    @SerialName("shipName")
    val shipName: String = "",
    @SerialName("shipNo")
    val shipNo: String = "",
    @SerialName("normNumber")
    val normNumber: String = "",
    @SerialName("mainCertificateNumber")
    val mainCertificateNumber: String = "",
    @SerialName("shipTotTon")
    val shipTotTon: String = "",
    @SerialName("jobWay")
    val jobWay: String = "",
    @SerialName("ownerTel")
    val ownerTel: String = "",
    @SerialName("shipType")
    val shipType: String = "",
    @SerialName("material")
    val material: String = "",
    @SerialName("ownerAddr")
    val ownerAddr: String = "",
    @SerialName("shipLength")
    val shipLength: String = "",
    @SerialName("shipBuildCompDate")
    val shipBuildCompDate: String = "",
    @SerialName("posUuid")
    val id: String = "",
    @SerialName("termNo")
    val mmsi: String = "",
    @SerialName("speed")
    val sog: String = "",
    @SerialName("longitude")
    val lng: String = "",
    @SerialName("latitude")
    val lat: String = "",
    @SerialName("azimuth")
    val azimuth: String = "",
    @SerialName("termType")
    val termType: String = "",
    @SerialName("rptTime")
    val rptTime: String = "",
    @SerialName("time")
    val time: String = ""
)
