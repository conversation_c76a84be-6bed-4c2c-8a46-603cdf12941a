package org.example.datasource.msa.guangxi

import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.CommonUtil.log
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest
import java.time.LocalDateTime

object GXTask {
    const val URL = "http://***********:8173"
    val endpointMap = mutableMapOf(
        "dyPositionNorth" to "/dyPositionNorth/list",
        "dyPositionSouth" to "/dyPositionNorth/list"
    )
    var pageSize = 20000
    var lastSearchTime: String = localDateTimeFormatter.format(LocalDateTime.now().minusDays(1))

    fun launchTask() {
        fetchShipsScheduledHttpCall(5 * 60 * 1000)
    }

    fun fetchShipsScheduledHttpCall(intervalMillis: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                val time = localDateTimeFormatter.format(LocalDateTime.now())
                endpointMap.forEach { (_, endpoint) ->
                    try {
                        val result = retryRequest<GXResponse> {
                            client.get("$URL$endpoint") {
                                contentType(ContentType.Application.Json)
                                url {
                                    parameters.append("startTime", lastSearchTime)
                                    parameters.append("size", pageSize.toString())
                                }
                            }.body()
                        }

                        if (pageSize < result.data.data.size)
                            pageSize = result.data.data.size + 1000

                        log.info("GXTask.fetchShipsScheduledHttpCall $endpoint result: ${result.data.data.size} ${result.data.data.elementAtOrNull(0)}")
                    } catch (e: Exception) {
                        log.error("GXTask.fetchShipsScheduledHttpCall HTTP call failed: ${e.message}")
                    }
                    delay(1000)
                }
                lastSearchTime = time
                delay(intervalMillis)
            }
        }
    }
}

data class ShipData(val mmsi: String)