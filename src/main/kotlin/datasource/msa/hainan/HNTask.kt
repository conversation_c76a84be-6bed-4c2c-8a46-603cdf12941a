package org.example.datasource.msa.hainan

import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.http.ContentType
import io.ktor.http.contentType
import io.ktor.http.isSuccess
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import org.example.util.CommonUtil.localDateFormatter
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.CommonUtil.log
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest
import org.example.util.Sm4Util.encryptEcb
import java.time.LocalDate
import java.time.LocalDateTime

object HNTask {
    const val URL = "http://*************:8001"
    const val AUTHORIZATION_STR = "Basic YTkwNTQyZjhiYWQzNGFlYmIwMzY0NTI0NmY1NGM5NmU6JDJhJDEwJEtvaURkRkZOVy54OUdkYkNJRzlna2U4VjNKcFptQjFxZXBRR3l6TlN5bG5zVHo1T1dTblFt"
    const val APPID = "a90542f8bad34aebb03645246f54c96e"
    const val ECB_KEY = "c1e3ff7bfb3f4afebdacad941bf00bc7"

    var token = ""
    var startTime = localDateTimeFormatter.format(LocalDateTime.now().minusHours(1)).replace(" ", "%20")
    var pageSize = 10000

    fun launchTask() {
        loginScheduledHttpCall(100 * 60 * 1000)
        fetchShipsScheduledHttpCall(5 * 60 * 1000)
    }

    fun loginScheduledHttpCall(intervalMillis: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                try {
                    refreshToken()
                } catch (e: Exception) {
                    log.error("HNTask.loginScheduledHttpCall HTTP call failed: ${e.message}")
                }
                delay(intervalMillis)
            }
        }
    }

    fun fetchShipsScheduledHttpCall(intervalMillis: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                try {
                    val endTime = localDateTimeFormatter.format(LocalDateTime.now()).replace(" ", "%20")
                    val plainText = "shipReg/hnFishShipAll?startTime=$startTime&endTime=$endTime&pageSize=$pageSize" +
                            "#${localDateFormatter.format(LocalDate.now())}#$APPID"
                    val cipherText = encryptEcb(ECB_KEY, plainText)
                    log.info("Hainan plainText: $plainText, endpoint: /api/$cipherText")

                    val result = retryRequest<HNResponse> {
                        client.get("$URL/api/$cipherText") {
                            contentType(ContentType.Application.Json)
                            header("Authorization", "Bearer $token")
                            header("Blade-auth", token)
                            header("appid", APPID)
                        }.body()
                    }

                    log.info("HNTask.fetchShipsScheduledHttpCall result: ${result.result.size}, ${result.result.elementAtOrNull(0)}")

                    if (pageSize < result.result.size)
                        pageSize = result.result.size + 1000
                    startTime = endTime
                } catch (e: Exception) {
                    // 海南局token有效期2小时，期间无法重新申请token
                    refreshToken()
                    log.error("HNTask.fetchShipsScheduledHttpCall HTTP call failed: ${e.message}")
                }
                delay(intervalMillis)
            }
        }
    }

    // 更新token
    private suspend fun refreshToken() {
        val response = retryRequest {
            client.post("$URL/blade-auth/oauth/token") {
                contentType(ContentType.Application.Json)
                header("Authorization", AUTHORIZATION_STR)
            }
        }

        token = if (response.status.isSuccess())
            response.body<LoginAuthSuccessResponse>().accessToken
        else
            response.body<LoginAuthFailedResponse>().token

        log.info("HNTask.loginScheduledHttpCall token: $token")
    }
}

fun main() {
    val str = "{\"msg\":\"请在79分钟后重新获取access_token，获取access_token的频率不得超过90分钟一次。有效期内token如下\",\"code\":401,\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJhbGwiXSwiZXhwIjoxNzUyODQxMTQxLCJqdGkiOiJlYzQxOGY0NC1hNGM0LTQ2ZGMtYmEyMC0yYzBjN2NkOWMzZDMiLCJjbGllbnRfaWQiOiJhOTA1NDJmOGJhZDM0YWViYjAzNjQ1MjQ2ZjU0Yzk2ZSJ9.xqY8d2xCTdNjzgGusz_-L0M_PWof7PeQXtrjAdQ15ng\"}"
    println(Json.decodeFromString<LoginAuthFailedResponse>(str).token)
}