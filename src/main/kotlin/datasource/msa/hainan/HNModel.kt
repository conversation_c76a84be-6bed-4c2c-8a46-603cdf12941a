package org.example.datasource.msa.hainan

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LoginAuthSuccessResponse(
    @SerialName("access_token")
    val accessToken: String,
    @SerialName("token_type")
    val tokenType: String,
    @SerialName("expires_in")
    val expiresIn: Int,
    @SerialName("scope")
    val scope: String
)

@Serializable
data class LoginAuthFailedResponse(
    @SerialName("msg")
    val msg: String,
    @SerialName("code")
    val code: Int,
    @SerialName("token")
    val token: String
)

@Serializable
data class HNResponse(
    @SerialName("result")
    val result: List<HNFishingData>,
    @SerialName("total")
    val total: Int,
    @SerialName("returnSize")
    val returnSize: Int,
    @SerialName("code")
    val code: Int,
    @SerialName("totalPages")
    val totalPages: Int,
    @SerialName("message")
    val message: String,
    @SerialName("status")
    val status: String
)

/**
 * class HainanResult {
 *     @RelatedProperty("id") var ID: String = ""
 *     @RelatedProperty("mmsi") var MMSI: String = ""
 *     @RelatedProperty("imo") var IMO: String = ""
 *     @RelatedProperty("callSign") var CALLSIGN: String = ""
 *     @RelatedProperty("name") var NAME: String = ""
 *     @RelatedProperty("type") var TYPE: String = ""
 *     @RelatedProperty("loa") var LOA: String = ""
 *     @RelatedProperty("bm") var BM: String = ""
 *     @RelatedProperty("eta") var ETA: String = ""
 *     @RelatedProperty("draught") var DRAUGHT: String = ""
 *     @RelatedProperty("dest") var DEST: String = ""
 *     @RelatedProperty("epfs") var EPFS: String = ""
 *     @RelatedProperty("mid") var MID: String = ""
 *     @RelatedProperty("umc") var UMC: String = ""
 *     @RelatedProperty("sn") var SN: String = ""
 *     @RelatedProperty("state") var STATE: String = ""
 *     @RelatedProperty("rot") var ROT: String = ""
 *     @RelatedProperty("sog") var SOG: String = ""
 *     @RelatedProperty("lng") var LNG: String = ""
 *     @RelatedProperty("lat") var LAT: String = ""
 *     @RelatedProperty("cog") var COG: String = ""
 *     @RelatedProperty("heading") var HEADING: String = ""
 *     var TIMESTIMP: String = ""
 * //        get() = dateTimeFormatter.format(timestampToLocalDateTime(field.toLongOrNull() ?: 0L, false))
 *     @RelatedProperty("fromType") var FROMTYPE: String = ""
 *     @RelatedProperty("time") var RCVTIME: String = ""
 * }
 *
 */
@Serializable
data class HNFishingData(
    @SerialName("ID")
    val id: String = "",
    @SerialName("MMSI")
    val mmsi: String = "",
    @SerialName("IMO")
    val imo: String = "",
    @SerialName("CALLSIGN")
    val callSign: String = "",
    @SerialName("NAME")
    val name: String = "",
    @SerialName("TYPE")
    val type: String = "",
    @SerialName("LOA")
    val loa: String = "",
    @SerialName("BM")
    val bm: String = "",
    @SerialName("ETA")
    val eta: String = "",
    @SerialName("DRAUGHT")
    val draught: String = "",
    @SerialName("DEST")
    val dest: String = "",
    @SerialName("EPFS")
    val epfs: String = "",
    @SerialName("MID")
    val mid: String = "",
    @SerialName("UMC")
    val umc: String = "",
    @SerialName("SN")
    val sn: String = "",
    @SerialName("STATE")
    val state: String = "",
    @SerialName("ROT")
    val rot: String = "",
    @SerialName("SOG")
    val sog: String = "",
    @SerialName("LNG")
    val lng: String = "",
    @SerialName("LAT")
    val lat: String = "",
    @SerialName("COG")
    val cog: String = "",
    @SerialName("HEADING")
    val heading: String = "",
    @SerialName("TIMESTAMP")
    val timestamp: String = "",
    @SerialName("FROMTYPE")
    val fromType: String = "",
    @SerialName("RCVTIME")
    val time: String = ""
)