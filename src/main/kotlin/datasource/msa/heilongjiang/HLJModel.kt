package org.example.datasource.msa.heilongjiang

import kotlinx.serialization.SerialName

data class HLJPositionRequest(
    val sender: String = "",
    val receiver: String = "",
    val data: HLJPositionData = HLJPositionData()
)

data class HLJPositionData(
    val random: String = "",
    val positions: List<HLJFishingData> = listOf()
)

data class HLJFishingData(
    val lng: String = "",
    val lat: String = "",
    val lngDir: String = "",
    val latDir: String = "",
    val valid: Boolean = true,
    @SerialName("directory")
    val cog: String = "",
    val height: String = "",
    @SerialName("speed")
    val sog: String = "",
    val dataTime: Long = 0L,
    val time: String = ""
)

data class HLJResponse(
    val data: String? = null,
    val time: Long = 0L,
    val rtnMsg: String = "",
    val rtnCode: String = ""
)