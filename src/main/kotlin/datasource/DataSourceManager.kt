package org.example.datasource

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.example.database.clickhouse.ClickHouseUtil.insertList
import org.example.util.CommonUtil.log

data class InsertTask(
    val list: List<Any>,
    val table: String,
    val retryCount: Int = 0
)

object DataSourceManager {
    const val MAX_CONCURRENCY = 5 // 同时最大并发数
    const val MAX_RETRY_COUNT = 1 // 最大重试次数
    val dataChannel = Channel<String>(capacity = Channel.Factory.UNLIMITED)
    val taskChannel = Channel<InsertTask>(capacity = Channel.UNLIMITED)

    val semaphore = Semaphore(MAX_CONCURRENCY)


    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun persistDataPeriodically() {
        while (true) {
            delay(5_000) // 每5秒
            val dataBatch = mutableListOf<String>()
            while (!dataChannel.isEmpty) {
                dataChannel.tryReceive().getOrNull()?.let { dataBatch.add(it) }
            }
            if (dataBatch.isNotEmpty()) {
                // 持久化到数据库或文件
                println("Persisting ${dataBatch.size} items")
//                persistToStorage(dataBatch)
            }
        }
    }

    fun startTaskProcessor(scope: CoroutineScope) {
        repeat(MAX_CONCURRENCY) {
            scope.launch(Dispatchers.IO) {
                for (task in taskChannel) {
                    processTask(task)
                }
            }
        }
    }

    suspend fun processTask(task: InsertTask) {
        semaphore.withPermit {
            try {
                insertList(task.list, task.table)
                log.info("Insert success for table ${task.table}")
            } catch (e: Exception) {
                println("Insert failed: ${e.message}")
                if (task.retryCount < MAX_RETRY_COUNT) {
                    log.error("Insert failed for table ${task.table}, retrying... (attempt ${task.retryCount + 1})")
                    taskChannel.send(task.copy(retryCount = task.retryCount + 1))
                } else {
                    log.error("Failed after retries: ${task.table}")
                }
            }
        }
    }

}