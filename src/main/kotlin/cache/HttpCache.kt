package org.example.cache

import com.github.benmanes.caffeine.cache.Caffeine
import java.util.concurrent.TimeUnit

object HttpCache {
    private val cache = Caffeine.newBuilder()
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .maximumSize(1000)
        .build<String, String>()

    fun getCache(id: String): String? {
        return cache.getIfPresent(id)
    }

    fun setCache(id: String, token: String) {
        cache.put(id, token)
    }
}