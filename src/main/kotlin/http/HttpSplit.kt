package org.example.http

import com.fasterxml.jackson.core.type.TypeReference
import io.ktor.http.HttpMethod
import org.apache.flink.api.connector.source.SourceSplit
import org.example.util.customObjectMapper
import java.io.Serializable

data class HttpSplit<T>(
    val config: HttpSplitConfig,
    val splitId: String
) : SourceSplit, Serializable {
    override fun splitId() = splitId
}

class HttpSplitConfig(
    val baseUrl: String = "",
    val endpoint: String = "",
    val fromType: String = ""
) : Serializable

