package org.example.http

import org.apache.flink.api.connector.source.SplitEnumerator
import org.apache.flink.api.connector.source.SplitEnumeratorContext
import org.example.model.BDFishingData

class HttpSplitEnumerator(
    private val context: SplitEnumeratorContext<HttpSplit<BDFishingData>>,
    private val httpSplitConfigs: List<HttpSplitConfig>
) : SplitEnumerator<HttpSplit<BDFishingData>, Void> {

    private val remainingSplits = httpSplitConfigs.mapIndexed { idx, config ->
        HttpSplit<BDFishingData>(config, "http-split-$idx")
    }.toMutableList()

    override fun start() {
    }

    private fun assignSplits(subtaskId: Int) {
        val registeredReaders = context.registeredReaders().keys.toList()
        if (registeredReaders.isEmpty()) return

        while (remainingSplits.isNotEmpty()) {
            val split = remainingSplits.removeAt(0)
            context.assignSplit(split, subtaskId)
        }
    }

    override fun handleSplitRequest(subtaskId: Int, requesterHostname: String?) {
        // 保守处理，防止未注册 subtask
        if (context.registeredReaders().containsKey(subtaskId)) {
            addReader(subtaskId)
        }
    }

    override fun addSplitsBack(splits: List<HttpSplit<BDFishingData>?>?, subtaskId: Int) {
    }

    override fun addReader(subtaskId: Int) {
        if (remainingSplits.isNotEmpty()) {
            val split = remainingSplits.removeAt(0)
            context.assignSplit(split, subtaskId)
            context.signalNoMoreSplits(subtaskId) // 可选，如果没有更多 split 分配
        }
    }


    override fun snapshotState(checkpointId: Long): Void? = null

    override fun close() {
    }
}