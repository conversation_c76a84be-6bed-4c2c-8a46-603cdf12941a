package org.example.http

import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.ContentType
import io.ktor.http.contentType
import io.ktor.http.parameters
import org.apache.flink.api.common.eventtime.WatermarkStrategy
import org.apache.flink.api.common.serialization.SimpleStringEncoder
import org.apache.flink.connector.file.sink.FileSink
import org.apache.flink.connector.jdbc.JdbcConnectionOptions
import org.apache.flink.connector.jdbc.JdbcSink
import org.apache.flink.connector.jdbc.JdbcStatementBuilder
import org.apache.flink.core.fs.Path
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.streaming.api.functions.sink.SinkFunction
import org.example.cache.HttpCache
import org.example.datasource.msa.guangxi.GXResponse
import org.example.kapt.BDFishingMapper
import org.example.model.BDFishingData
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest
import org.example.util.customObjectMapper
import java.time.LocalDateTime

fun main() {
    val gxHttpSplitConfig = HttpSplitConfig("http://198.21.1.78:8173", "/dyPositionNorth/list", "15")
    val gxHttpSplitConfig2 = HttpSplitConfig("http://198.21.1.78:8173", "/dyPositionNorth/list", "15")

    val httpSource = HttpSource(listOf(gxHttpSplitConfig, gxHttpSplitConfig2))
    val env = StreamExecutionEnvironment.getExecutionEnvironment()
    val sink = FileSink
        .forRowFormat(Path("C:\\projects\\beidou-fishing-test\\data\\"), SimpleStringEncoder<String>("UTF-8"))
        .build()

    val stream = env.fromSource(httpSource, WatermarkStrategy.noWatermarks(), "http-source")

    stream.map { customObjectMapper.writeValueAsString(it) }.sinkTo(sink)
//    stream.addSink(sink2)

    env.execute()
}