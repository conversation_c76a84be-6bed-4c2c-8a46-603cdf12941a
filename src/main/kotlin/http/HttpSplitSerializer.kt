package org.example.http

import org.apache.flink.core.io.SimpleVersionedSerializer
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable

class HttpSplitSerializer<T : Serializable> : SimpleVersionedSerializer<HttpSplit<T>> {
    override fun getVersion(): Int = 1

    override fun serialize(split: HttpSplit<T>): ByteArray {
        // 使用 Java 原生序列化（或你可以用 Kryo/FST）
        val byteOut = ByteArrayOutputStream()
        val objOut = ObjectOutputStream(byteOut)
        objOut.writeObject(split)
        objOut.flush()
        return byteOut.toByteArray()
    }

    @Suppress("UNCHECKED_CAST")
    override fun deserialize(version: Int, serialized: ByteArray): HttpSplit<T> {
        val byteIn = ByteArrayInputStream(serialized)
        val objIn = ObjectInputStream(byteIn)
        return objIn.readObject() as HttpSplit<T>
    }

}
