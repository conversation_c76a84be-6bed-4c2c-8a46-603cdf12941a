package org.example.http

import org.apache.flink.api.connector.source.Boundedness
import org.apache.flink.api.connector.source.Source
import org.apache.flink.api.connector.source.SourceReader
import org.apache.flink.api.connector.source.SourceReaderContext
import org.apache.flink.api.connector.source.SplitEnumerator
import org.apache.flink.api.connector.source.SplitEnumeratorContext
import org.apache.flink.core.io.SimpleVersionedSerializer
import org.example.model.BDFishingData
import org.example.netty.NoOpSerializer

class HttpSource(
    private val httpSplitConfigs: List<HttpSplitConfig>
) : Source<BDFishingData, HttpSplit<BDFishingData>, Void> {
    override fun getBoundedness(): Boundedness? {
        return Boundedness.CONTINUOUS_UNBOUNDED
    }

    override fun createEnumerator(enumContext: SplitEnumeratorContext<HttpSplit<BDFishingData>>): SplitEnumerator<HttpSplit<BDFishingData>, Void> {
        return HttpSplitEnumerator(enumContext, httpSplitConfigs)
    }

    override fun restoreEnumerator(
        enumContext: SplitEnumeratorContext<HttpSplit<BDFishingData>>,
        checkpoint: Void
    ): SplitEnumerator<HttpSplit<BDFishingData>, Void>? {
        return HttpSplitEnumerator(enumContext, httpSplitConfigs)
    }

    override fun getSplitSerializer(): SimpleVersionedSerializer<HttpSplit<BDFishingData>>? {
        return HttpSplitSerializer()
    }

    override fun getEnumeratorCheckpointSerializer(): SimpleVersionedSerializer<Void>? {
        return NoOpSerializer
    }

    override fun createReader(readerContext: SourceReaderContext): SourceReader<BDFishingData, HttpSplit<BDFishingData>>? {
        return HttpSourceReader(readerContext)
    }
}