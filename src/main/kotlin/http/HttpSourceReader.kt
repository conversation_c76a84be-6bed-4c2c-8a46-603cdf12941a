package org.example.http

import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.contentType
import io.ktor.http.parameters
import io.ktor.server.util.url
import kotlinx.coroutines.runBlocking
import org.apache.flink.api.connector.source.ReaderOutput
import org.apache.flink.api.connector.source.SourceReader
import org.apache.flink.api.connector.source.SourceReaderContext
import org.apache.flink.core.io.InputStatus
import org.example.cache.HttpCache
import org.example.datasource.msa.guangxi.GXResponse
import org.example.kapt.BDFishingMapper
import org.example.model.BDFishingData
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.CommonUtil.log
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest
import java.time.LocalDateTime
import java.util.concurrent.BlockingQueue
import java.util.concurrent.CompletableFuture
import java.util.concurrent.LinkedBlockingQueue
import kotlin.concurrent.thread

class HttpSourceReader(
    private val context: SourceReaderContext
) : SourceReader<BDFishingData, HttpSplit<BDFishingData>> {

    private val available = CompletableFuture<Void?>().apply { complete(null) }
    private val splits = mutableListOf<HttpSplit<BDFishingData>>()
    private val nextCallTimeMap = mutableMapOf<HttpSplit<BDFishingData>, Long>()
    private val fetcher: HttpFetcher<BDFishingData> = object : HttpFetcher<BDFishingData> {
        override suspend fun fetch(baseUrl: String, endpoint: String, fromType: String): List<BDFishingData> {
            val time = localDateTimeFormatter.format(LocalDateTime.now())
            val startTime = HttpCache.getCache("$fromType-$endpoint-startTime") ?: localDateTimeFormatter.format(LocalDateTime.now().minusDays(1))
            val pageSize = HttpCache.getCache("$fromType-$endpoint-size") ?: "20000"
            log.info("HttpSourceReader.fetch ${baseUrl + endpoint} startTime: $startTime, pageSize: $pageSize")
            val result = retryRequest<GXResponse> {
                client.get(baseUrl + endpoint) {
                    contentType(ContentType.Application.Json)
                    url {
                        parameters.append("startTime", startTime)
                        parameters.append("size", pageSize)
                    }
                }.body()
            }

            if ((pageSize.toIntOrNull() ?: 0) < result.data.data.size)
                HttpCache.setCache("$fromType-$endpoint-size", (result.data.data.size + 1000).toString())
            HttpCache.setCache("$fromType-$endpoint-startTime", time)

            return result.data.data.map { BDFishingMapper.INSTANCE.toBDFishingData(it).apply { this.fromType = fromType } }
        }
    }

    override fun start(): Unit = Unit

    override fun pollNext(output: ReaderOutput<BDFishingData?>?): InputStatus? {
        val now = System.currentTimeMillis()
        var hasEmitted = false

        for (split in splits) {
            val nextTime = nextCallTimeMap[split] ?: 0L
            if (now >= nextTime) {
                // 调用http请求，假设同步调用，实际建议异步+缓存
                val data = mutableListOf<BDFishingData>()
                runBlocking {
                    data.addAll(fetcher.fetch(split.config.baseUrl, split.config.endpoint, split.config.fromType))
                }

                log.info("HttpSourceReader.pollNext data size: ${data.size}, fromType: ${split.config.fromType}")

                if (data.isNotEmpty()) {
                    data.forEach { output?.collect(it) }
                    hasEmitted = true
                }
                nextCallTimeMap[split] = now + 20_000L
            }
        }

        return if (hasEmitted) InputStatus.MORE_AVAILABLE else InputStatus.NOTHING_AVAILABLE
    }

    override fun snapshotState(checkpointId: Long): List<HttpSplit<BDFishingData>?>? = listOf()

    override fun isAvailable(): CompletableFuture<Void?>? = available

    override fun addSplits(splits: List<HttpSplit<BDFishingData>>?) {
        splits?.forEach { split ->
            this.splits.add(split)
            nextCallTimeMap[split] = 0L // 初始化为立即可调用
        }
    }

    override fun notifyNoMoreSplits() {}

    override fun close() {}
}