//package org.example.work.`202507`
//
//import io.ktor.http.cio.decodeChunked
//import kotlinx.coroutines.*
//import kotlinx.coroutines.sync.Semaphore
//import kotlinx.coroutines.sync.withPermit
//import org.example.database.clickhouse.ClickHouseUtil
//import org.example.util.CommonUtil.localDateTimeFormatter
//import java.io.File
//import java.time.LocalDateTime
//import java.time.format.DateTimeFormatter
//
//object Port {
//    val shipSet = mutableSetOf<String>()
//    val chinaShips = mutableSetOf<String>()
//    val outputPath = "F:\\project\\tool\\output\\"
//    val filenameFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
//
//    // 2024-06-01 00:00:00
//    val startTime: LocalDateTime = LocalDateTime.parse("2025-04-01T00:00:00")
//    val endTime: LocalDateTime = LocalDateTime.parse("2025-06-30T23:59:59")
//
//    // 时间区间
//    data class TimeInterval(
//        val startTime: LocalDateTime,
//        val endTime: LocalDateTime
//    )
//
//    init {
//        try {
//            File("F:\\project\\tool\\shipSet.txt").bufferedReader().forEachLine {
//                if (it.isNotEmpty())
//                    shipSet.add(it)
//            }
//
//            File("F:\\project\\tool\\china_ship.txt").bufferedReader().forEachLine {
//                if (it.isNotEmpty())
//                    chinaShips.add(it)
//            }
//        } catch (e: Exception) {
//            println(e.message)
//        }
//
//        println("shipSet size: ${shipSet.size}, chinaShips size: ${chinaShips.size}")
//    }
//
//    /**
//     * 将startTime和endTime按照每天切分成时间区间列表
//     */
//    fun getTimeIntervalList(): List<TimeInterval> {
//        val timeIntervalList = mutableListOf<TimeInterval>()
//        var time = startTime
//        while (time < endTime) {
//            timeIntervalList.add(TimeInterval(time, time.plusDays(1)))
//            time = time.plusDays(1)
//        }
//        return timeIntervalList
//    }
//
////    fun run() {
////        getTimeIntervalList().forEach { timeInterval ->
////            val sql = """
////                SELECT date,message FROM ais_datagram_all WHERE date between '${localDateTimeFormatter.format(timeInterval.startTime)}'
////                and '${localDateTimeFormatter.format(timeInterval.endTime)}' AND
////                mmsi IN (${shipSet.joinToString(",") { "'$it'" }}) and messageId = '5';
////            """.trimIndent()
////            ClickHouseUtil.executeQuery(sql)
////        }
////    }
//
//    fun runConcurrentQuery(maxConcurrent: Int = 5) = runBlocking {
//        val semaphore = Semaphore(maxConcurrent)
//
//        val jobs = mutableListOf<Job>()
//
//        shipSet.clear()
//        File("F:\\project\\tool\\port").listFiles().filter { file -> file.isFile && file.extension == "txt" }.forEach { file ->
////            File(outputPath + file.nameWithoutExtension).let { if(!it.exists()) it.mkdirs() }
//            println(file.name)
//            file.bufferedReader().forEachLine {
//                if (it.isNotEmpty())
//                    shipSet.add(it)
//            }
//        }
//        println("shipSet size: ${shipSet.size}")
//
//        val timeIntervals = getTimeIntervalList()
//        for (timeInterval in timeIntervals) {
//            shipSet.chunked(11000).forEach { ships ->
//                val sql = """
//                    SELECT date,message FROM ais_wxdatagramtest_all WHERE date between '${localDateTimeFormatter.format(timeInterval.startTime)}' and '${localDateTimeFormatter.format(timeInterval.endTime)}' AND mmsi IN (${ships.joinToString(",") { "'$it'" }}) and messageId = '5' order by date;
//                """.trimIndent()
//
//                val job = launch(Dispatchers.IO) {
//                    semaphore.withPermit {
//                        println("${localDateTimeFormatter.format(timeInterval.startTime)} - ${localDateTimeFormatter.format(timeInterval.endTime)}")
//                        val list = ClickHouseUtil.executeQuery(sql)
//
//                        // 将list写入文件
//                        val fileName = "${filenameFormatter.format(timeInterval.startTime)}.txt"
//                        File(outputPath + fileName).appendText(list.joinToString("\n") {
//                            "${it.getOrDefault("date", "")}\t${it.getOrDefault("message", "")}"
//                        })
//                    }
//                }
//                jobs.add(job)
//            }
//        }
//
//        jobs.joinAll()  // 等待所有任务完成
//    }
//
//    fun queryPosition(maxConcurrent: Int = 3) = runBlocking {
//        val semaphore = Semaphore(maxConcurrent)
//        val timeIntervals = getTimeIntervalList()
//        val jobs = mutableListOf<Job>()
//
//        for (timeInterval in timeIntervals) {
//            val sql = """
//                SELECT date,mmsi,code,speed,geohash,lat,lng,course,heading FROM ais_wxpositiontest_all WHERE date between '${localDateTimeFormatter.format(timeInterval.startTime)}' and '${localDateTimeFormatter.format(timeInterval.endTime)}' AND mmsi IN (${chinaShips.joinToString(",") { "'$it'" }}) order by date;
//            """.trimIndent()
//
//            val job = launch(Dispatchers.IO) {
//                semaphore.withPermit {
//                    println("${localDateTimeFormatter.format(timeInterval.startTime)} - ${localDateTimeFormatter.format(timeInterval.endTime)}")
//                    val list = ClickHouseUtil.executeQuery(sql)
//
//                    // 将list写入文件
//                    val fileName = "${filenameFormatter.format(timeInterval.startTime)}.txt"
//                    File(outputPath + fileName).appendText(list.joinToString("\n") {
//                        "${it.getOrDefault("date", "")}\t${it.getOrDefault("mmsi", "")}\t${it.getOrDefault("code", "")}\t${it.getOrDefault("speed", "")}\t${it.getOrDefault("geohash", "")}\t${it.getOrDefault("lat", "")}\t${it.getOrDefault("lng", "")}\t${it.getOrDefault("course", "")}\t${it.getOrDefault("heading", "")}"
//                    })
//                }
//            }
//            jobs.add(job)
//        }
//
//        jobs.joinAll()  // 等待所有任务完成
//    }
//
//    fun queryShipDate() {
//        val sqlList = """
//            霍尔木兹海峡 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(56.206055,26.175159),(56.381836,26.009893),(56.219788,25.567220),(56.346130,24.958670),(56.464233,24.676970),(57.598572,25.775161),(56.895447,27.166695),(56.181335,27.264396),(55.541382,27.032217),(54.909668,26.632729),(54.791565,26.500073),(55.016785,25.849337),(55.505676,25.430873),(55.950623,25.713312),(56.206055,26.175159)])
//            苏伊士运河 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(32.232169,31.549575),(32.267667,31.562617),(32.328083,31.515767),(32.622800,31.399733),(32.535600,31.255950),(32.398100,31.213050),(32.445917,30.452283),(32.704100,30.238833),(32.697367,29.655817),(32.438517,29.655217),(32.372583,29.694617),(32.450417,30.147883),(32.283333,30.242900),(32.221800,31.208483),(32.081583,31.349883),(32.232169,31.549575)])
//            龙目-望加锡通道 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(120.459595,13.432367),(121.085815,12.361466),(122.058105,11.781325),(121.923523,10.474308),(120.305786,5.222247),(119.926758,0.417477),(119.113770,-2.613839),(118.586426,-5.058114),(117.647095,-7.705548),(117.026367,-8.830795),(116.998901,-9.627830),(115.147705,-9.248514),(115.169678,-8.847079),(115.164185,-8.347388),(118.913269,0.961259),(119.586182,10.531020),(119.487305,11.070603),(119.855347,12.259496),(119.723511,13.036669),(119.866333,13.119605),(120.459595,13.432367)])
//            巴拿马运河 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(-79.935962,9.538458),(-79.891830,9.565167),(-79.731217,9.564483),(-79.718047,9.481793),(-79.816542,9.404480),(-79.815300,9.239452),(-79.796448,9.173397),(-79.664237,9.107521),(-79.518482,8.986240),(-79.436083,9.001739),(-79.314485,8.829260),(-79.570105,8.700940),(-79.818233,9.053277),(-79.983028,9.164865),(-79.947323,9.284157),(-79.985400,9.355033),(-79.999320,9.475742),(-79.935962,9.538458)])
//            马六甲海峡 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(94.965820,6.446318),(96.108398,7.155400),(99.234009,7.988518),(100.468971,6.076773),(101.228027,3.699819),(101.986084,2.597377),(103.590008,1.447295),(104.002075,1.347047),(104.247894,1.397872),(104.289093,1.514564),(104.479980,1.441803),(104.589844,1.292157),(104.551392,1.171336),(104.015808,1.167217),(103.842773,1.064239),(103.087463,0.942035),(101.813049,1.421210),(100.244751,2.318855),(99.769592,3.019841),(98.349609,3.976601),(97.476196,5.112830),(96.141357,5.186688),(95.218506,5.900189),(94.965820,6.446318)])
//            曼德海峡 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(42.544809,13.226577),(43.246307,13.415002),(43.266907,13.247966),(43.522339,12.798410),(43.913727,12.663117),(43.952179,12.604156),(43.417282,12.150773),(43.104172,12.669817),(43.104172,12.705340),(42.600174,13.031318),(42.544809,13.226577)])
//            好望角 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(16.603907,-32.867953),(28.035938,-32.867953),(28.035938,-38.136988),(16.603907,-38.136988),(16.603907,-32.867953)])
//            直布罗陀海峡 from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(-6.149723,36.294632),(-5.608460,36.022452),(-5.340118,36.115690),(-5.276184,35.889050),(-5.910645,35.777714),(-5.987300,35.556809),(-6.149723,36.294632)])
//        """.trimIndent()
//        val map = sqlList.split("\n").associate {
//            val data = it.split(" ", limit = 2)
//            data.first() to data.last()
//        }
//
//        File("F:\\project\\tool\\port").listFiles().filter { file -> file.isFile && file.extension == "txt" }.forEach { file ->
////            File(outputPath + file.nameWithoutExtension).let { if(!it.exists()) it.mkdirs() }
//            println(file.name)
//            file.bufferedReader().forEachLine { mmsi ->
//                if (mmsi.isNotEmpty()) {
//                    val sql = """
//                        SELECT min(date) as first_date, max(date) as last_date ${map.getOrDefault(file.nameWithoutExtension, "")} and mmsi = '$mmsi';
//                    """.trimIndent()
//                    println(sql)
//
//                    val list = ClickHouseUtil.executeQuery(sql)
//                    File(outputPath + "${file.nameWithoutExtension}_ship_date.txt").appendText(list.joinToString("\n") {
//                        "$mmsi\t${it.getOrDefault("first_date", "")}\t${it.getOrDefault("last_date", "")}\n"
//                    })
//                }
//            }
//        }
//    }
//
//    fun queryDistinctShips() = runBlocking {
//        val semaphore = Semaphore(3)
//        val jobs = mutableListOf<Job>()
//
//        val sqlList = """
//            霍尔木兹海峡 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(56.206055,26.175159),(56.381836,26.009893),(56.219788,25.567220),(56.346130,24.958670),(56.464233,24.676970),(57.598572,25.775161),(56.895447,27.166695),(56.181335,27.264396),(55.541382,27.032217),(54.909668,26.632729),(54.791565,26.500073),(55.016785,25.849337),(55.505676,25.430873),(55.950623,25.713312),(56.206055,26.175159)]);
//            苏伊士运河 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(32.232169,31.549575),(32.267667,31.562617),(32.328083,31.515767),(32.622800,31.399733),(32.535600,31.255950),(32.398100,31.213050),(32.445917,30.452283),(32.704100,30.238833),(32.697367,29.655817),(32.438517,29.655217),(32.372583,29.694617),(32.450417,30.147883),(32.283333,30.242900),(32.221800,31.208483),(32.081583,31.349883),(32.232169,31.549575)]);
//            龙目-望加锡通道 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(120.459595,13.432367),(121.085815,12.361466),(122.058105,11.781325),(121.923523,10.474308),(120.305786,5.222247),(119.926758,0.417477),(119.113770,-2.613839),(118.586426,-5.058114),(117.647095,-7.705548),(117.026367,-8.830795),(116.998901,-9.627830),(115.147705,-9.248514),(115.169678,-8.847079),(115.164185,-8.347388),(118.913269,0.961259),(119.586182,10.531020),(119.487305,11.070603),(119.855347,12.259496),(119.723511,13.036669),(119.866333,13.119605),(120.459595,13.432367)]);
//            巴拿马运河 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(-79.935962,9.538458),(-79.891830,9.565167),(-79.731217,9.564483),(-79.718047,9.481793),(-79.816542,9.404480),(-79.815300,9.239452),(-79.796448,9.173397),(-79.664237,9.107521),(-79.518482,8.986240),(-79.436083,9.001739),(-79.314485,8.829260),(-79.570105,8.700940),(-79.818233,9.053277),(-79.983028,9.164865),(-79.947323,9.284157),(-79.985400,9.355033),(-79.999320,9.475742),(-79.935962,9.538458)]);
//            马六甲海峡 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(94.965820,6.446318),(96.108398,7.155400),(99.234009,7.988518),(100.468971,6.076773),(101.228027,3.699819),(101.986084,2.597377),(103.590008,1.447295),(104.002075,1.347047),(104.247894,1.397872),(104.289093,1.514564),(104.479980,1.441803),(104.589844,1.292157),(104.551392,1.171336),(104.015808,1.167217),(103.842773,1.064239),(103.087463,0.942035),(101.813049,1.421210),(100.244751,2.318855),(99.769592,3.019841),(98.349609,3.976601),(97.476196,5.112830),(96.141357,5.186688),(95.218506,5.900189),(94.965820,6.446318)]);
//            曼德海峡 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(42.544809,13.226577),(43.246307,13.415002),(43.266907,13.247966),(43.522339,12.798410),(43.913727,12.663117),(43.952179,12.604156),(43.417282,12.150773),(43.104172,12.669817),(43.104172,12.705340),(42.600174,13.031318),(42.544809,13.226577)]);
//            好望角 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(16.603907,-32.867953),(28.035938,-32.867953),(28.035938,-38.136988),(16.603907,-38.136988),(16.603907,-32.867953)]);
//            直布罗陀海峡 select distinct mmsi from ais_wxdatagramtest_all where date between '2025-06-01 00:00:00' and '2025-06-30 23:59:59' and pointInPolygon((lng,lat), [(-6.149723,36.294632),(-5.608460,36.022452),(-5.340118,36.115690),(-5.276184,35.889050),(-5.910645,35.777714),(-5.987300,35.556809),(-6.149723,36.294632)]);
//        """.trimIndent()
//
//        sqlList.split("\n").forEach {
//            val name = it.split(" ", limit = 2).first()
//            val sql = it.split(" ", limit = 2).last()
//
//            val job = launch(Dispatchers.IO) {
//                semaphore.withPermit {
//                    println("Executing $name $sql")
//                    val list = ClickHouseUtil.executeQuery(sql)
//
//                    File(outputPath + "${name}.txt").writeText(list.joinToString("\n") { data ->
//                        data.getOrDefault("mmsi", "")
//                    })
//                }
//            }
//            jobs.add(job)
//        }
//
//        jobs.joinAll()  // 等待所有任务完成
//    }
//}