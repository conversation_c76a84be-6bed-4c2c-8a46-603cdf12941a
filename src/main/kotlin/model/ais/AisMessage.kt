package org.example.model.ais

import org.example.annotations.UniqueId
import org.example.annotations.Bit
import org.example.annotations.Indicator
import org.example.util.BitReader

/**
 * AIS消息
 */
open class AisMessage {
    @Bit(0, 6, "消息ID") var msgId: Int = 0
    @Bit(1, 2, "转发指示符") var repeat: Int = 0
    @Bit(2, 30, "用户ID") var userId: Long = 0L

    constructor()

    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
    }
}

/**
 * 消息1、2、3：位置报告
 *
 * 位置报告应由移动台定期发出。
 */
@UniqueId(1)
open class AisMessage1: AisMessage {
    @Bit(3, 4, "航行状态") var navigationalStatus: Int = 0
    @Bit(4, 8, "转向率") var rateOfTurn: Int = 0
    @Bit(5, 10, "对地航速") var speedOverGround: Int = 0
    @Bit(6, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(7, 28, "经度") var longitude: Long = 0L
    @Bit(8, 27, "纬度") var latitude: Long = 0L
    @Bit(9, 12, "对地航向") var courseOverGround: Int = 0
    @Bit(10, 9, "真航向") var heading: Int = 0
    @Bit(11, 6, "时间戳") var second: Int = 0
    @Bit(12, 2, "特殊操纵指示符") var maneuverIndicator: Int = 0
    @Bit(13, 3, "备用") var spare: Int = 0
    @Bit(14, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(16, 2, "同步状态") var syncState: Int = 0
    @Bit(17, 3, "时隙超时") var slotTimeout: Int = 0
    @Bit(18, 14, "子消息") var subMessage: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        navigationalStatus = reader.readBits(4)
        rateOfTurn = reader.readBits(8)
        speedOverGround = reader.readBits(10)
        positionAccuracy = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        courseOverGround = reader.readBits(12)
        heading = reader.readBits(9)
        second = reader.readBits(6)
        maneuverIndicator = reader.readBits(2)
        spare = reader.readBits(3)
        raimFlag = reader.readBits(1)
        syncState = reader.readBits(2)
        slotTimeout = reader.readBits(3)
        subMessage = reader.readBits(14)
    }
}

@UniqueId(2)
class AisMessage2: AisMessage1 {
    constructor()


    constructor(byteArray: ByteArray): super(byteArray)
}

@UniqueId(3)
class AisMessage3: AisMessage1 {
    constructor()


    constructor(byteArray: ByteArray): super(byteArray)
}

/**
 * 消息4：基站报告
 *
 * 消息11：协调世界时和日期响应
 * 应用于报告在相同时间、位置的UTC时间和日期。基站应在其周期性传输中使用消息4。AIS台采用消息4确定其是否处在响应消息20和23的120海里内。移动台应仅在对由消息10的询问进行的响应中输出消息11。
 * 消息11只有作为一条UTC请求消息（消息10）的响应时才发送。UTC和日期响应在接收UTC请求消息的那条信道上发送。
 */
@UniqueId(4)
open class AisMessage4: AisMessage {
    @Bit(3, 14, "UTC年") var utcYear: Int = 0
    @Bit(4, 4, "UTC月") var utcMonth: Int = 0
    @Bit(5, 5, "UTC日") var utcDay: Int = 0
    @Bit(6, 5, "UTC时") var utcHour: Int = 0
    @Bit(7, 6, "UTC分") var utcMinute: Int = 0
    @Bit(8, 6, "UTC秒") var utcSecond: Int = 0
    @Bit(9, 1, "位置准确度") var posAcc: Int = 0
    @Bit(10, 28, "经度") var longitude: Long = 0L
    @Bit(11, 27, "纬度") var latitude: Long = 0L
    @Bit(12, 4, "定位系统类型") var posType: Int = 0
    @Bit(13, 1, "传输控制") var transmissionControl: Int = 0
    @Bit(14, 9, "备用") var spare: Int = 0
    @Bit(15, 1, "RAIM标志") var raim: Int = 0
    @Bit(16, 2, "同步状态") var syncState: Int = 0
    @Bit(17, 3, "时隙超时") var slotTimeout: Int = 0
    @Bit(18, 14, "子消息") var subMessage: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        utcYear = reader.readBits(14)
        utcMonth = reader.readBits(4)
        utcDay = reader.readBits(5)
        utcHour = reader.readBits(5)
        utcMinute = reader.readBits(6)
        utcSecond = reader.readBits(6)
        posAcc = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        posType = reader.readBits(4)
        transmissionControl = reader.readBits(1)
        spare = reader.readBits(9)
        raim = reader.readBits(1)
        syncState = reader.readBits(2)
        slotTimeout = reader.readBits(3)
        subMessage = reader.readBits(14)
    }
}

/**
 * 消息5：船舶静态和航行相关数据
 *
 * 应仅由A类船载和SAR航空器SIS台用于报告静态或航行相关数据。
 */
@UniqueId(5)
class AisMessage5: AisMessage {
    @Bit(3, 2, "AIS版本指示符") var versionIndicator: Int = 0
    @Bit(4, 30, "IMO号") var imo: Long = 0L
    @Bit(5, 42, "呼号", true) var callSign: String = ""
    @Bit(6, 120, "船名", true) var shipName: String = ""
    @Bit(7, 8, "船舶类型") var shipType: Int = 0
    @Bit(8, 9, "船首尺寸") var toBow: Int = 0
    @Bit(9, 9, "船尾尺寸") var toStern: Int = 0
    @Bit(10, 6, "左舷尺寸") var toPort: Int = 0
    @Bit(11, 6, "右舷尺寸") var toStarboard: Int = 0
    @Bit(12, 4, "电子定位装置类型") var epfd: Int = 0
    @Bit(13, 4, "ETA月") var month: Int = 0
    @Bit(14, 5, "ETA日") var day: Int = 0
    @Bit(15, 5, "ETA时") var hour: Int = 0
    @Bit(16, 6, "ETA分") var minute: Int = 0
    @Bit(17, 8, "吃水深度") var draught: Int = 0
    @Bit(18, 120, "目的地", true) var destination: String = ""
    @Bit(19, 1, "数据终端设备") var dte: Int = 0
    @Bit(20, 1, "备用") var spare: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        versionIndicator = reader.readBits(2)
        imo = reader.readBits(30).toLong()
        callSign = buildString {
            for (i in 0 until 42 step 6) {
                var code = reader.readBits(6)
                if (code < 31)
                    code += 64
                append(code.toChar())
            }
        }
        shipName = buildString {
            for (i in 0 until 120 step 6) {
                var code = reader.readBits(6)
                if (code < 31)
                    code += 64
                append(code.toChar())
            }
        }
        shipType = reader.readBits(8)
        toBow = reader.readBits(9)
        toStern = reader.readBits(9)
        toPort = reader.readBits(6)
        toStarboard = reader.readBits(6)
        epfd = reader.readBits(4)
        month = reader.readBits(4)
        day = reader.readBits(5)
        hour = reader.readBits(5)
        minute = reader.readBits(6)
        draught = reader.readBits(8)
        destination = buildString {
            for (i in 0 until 120 step 6) {
                var code = reader.readBits(6)
                if (code < 31)
                    code += 64
                append(code.toChar())
            }
        }
        dte = reader.readBits(1)
        spare = reader.readBits(1)
    }
}

/**
 * 消息6：寻址二进制消息
 *
 * 寻址二进制消息的长度根据二进制数据的量应可以变化。该长度应在1到5个时隙之间变化。见附件5的第2.1节中的应用标识符。
 */
@UniqueId(6)
class AisMessage6: AisMessage {
    @Bit(3, 2, "序列编号") var sequenceNumber: Int = 0
    @Bit(4, 30, "目的地ID") var destinationId: Long = 0L
    @Bit(5, 1, "重发标志") var retransmitFlag: Int = 0
    @Bit(6, 1, "备用") var spare: Int = 0
    @Bit(7, 0, "二进制数据") var binaryData: ByteArray = byteArrayOf()

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        sequenceNumber = reader.readBits(2)
        destinationId = reader.readBits(30).toLong()
        retransmitFlag = reader.readBits(1)
        spare = reader.readBits(1)
        binaryData = reader.readBitsToByteArray((byteArray.size - 9) * 8)
    }
}

/**
 * 消息7：二进制确认
 *
 * 消息13：安全相关确认
 * 消息7应作为在收到高达四条消息6时的确认（见附件2的第5.3.1节）且应在收到寻址消息确认的那条信道上发送。
 * 消息13应作为在收到高达四条消息12时的确认（见附件2的第5.3.1节）且应在收到寻址消息确认的那条信道上发送。
 * 这些确认应仅适用于VHF数据链路（见附件2的第5.3.1节）。对于确认应用必须采用其他方法。
 */
@UniqueId(7)
open class AisMessage7: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 30, "目的地ID1") var destinationId1: Long = 0L
    @Bit(5, 2, "序列号1") var sequenceNumber1: Int = 0
    @Bit(6, 30, "目的地ID2") var destinationId2: Long = 0L
    @Bit(7, 2, "序列号2") var sequenceNumber2: Int = 0
    @Bit(8, 30, "目的地ID3") var destinationId3: Long = 0L
    @Bit(9, 2, "序列号3") var sequenceNumber3: Int = 0
    @Bit(10, 30, "目的地ID4") var destinationId4: Long = 0L
    @Bit(11, 2, "序列号4") var sequenceNumber4: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        destinationId1 = reader.readBits(30).toLong()
        sequenceNumber1 = reader.readBits(2)
        destinationId2 = reader.readBits(30).toLong()
        sequenceNumber2 = reader.readBits(2)
        destinationId3 = reader.readBits(30).toLong()
        sequenceNumber3 = reader.readBits(2)
        destinationId4 = reader.readBits(30).toLong()
        sequenceNumber4 = reader.readBits(2)
    }
}

/**
 * 消息8：二进制广播消息
 *
 * 该消息的长度根据二进制数据的量应可以变化。该长度应在1到5个时隙之间变化。
 */
@UniqueId(8)
class AisMessage8: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 0, "二进制数据") var binaryData: ByteArray = byteArrayOf() // 420 bits for text, adjust as needed

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        binaryData = reader.readBitsToByteArray(byteArray.size * 8 - 40)
    }
}

/**
 * 消息9：标准的搜救航空器位置报告
 *
 * 该消息应用做参与搜救行动的航空器的标准位置报告。参与搜救行动的航空器之外的台站不应发送此消息。该消息的默认报告间隔应为10 s。
 */
@UniqueId(9)
class AisMessage9: AisMessage {
    @Bit(3, 12, "GNSS高度") var gnssHeight: Int = 0
    @Bit(4, 10, "对地航速") var sog: Int = 0
    @Bit(5, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(6, 28, "经度") var longitude: Long = 0L
    @Bit(7, 27, "纬度") var latitude: Long = 0L
    @Bit(8, 12, "对地航向") var cog: Int = 0
    @Bit(9, 6, "时间戳") var second: Int = 0
    @Bit(10, 1, "高度传感器") var altitudeSensor: Int = 0
    @Bit(11, 7, "备用") var spare: Int = 0
    @Bit(12, 1, "数据终端设备") var dte: Int = 0
    @Bit(13, 3, "备用2") var spare2: Int = 0
    @Bit(14, 1, "指配模式标志") var assignedModeFlag: Int = 0
    @Bit(15, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(16, 1, "通信状态选择器标志") var commStateSelectorFlag: Int = 0
    @Bit(17, 19, "通信状态") var commState: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        gnssHeight = reader.readBits(12)
        sog = reader.readBits(10)
        positionAccuracy = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        cog = reader.readBits(12)
        second = reader.readBits(6)
        altitudeSensor = reader.readBits(1)
        spare = reader.readBits(7)
        dte = reader.readBits(1)
        spare2 = reader.readBits(3)
        assignedModeFlag = reader.readBits(1)
        raimFlag = reader.readBits(1)
        commStateSelectorFlag = reader.readBits(1)
        commState = reader.readBits(19)
    }
}

/**
 * 消息10：协调世界时/日期询问

 * 在一个台站请求另一个台站提供UTC和日期时应采用该消息。
 */
@UniqueId(10)
class AisMessage10: AisMessage {
    @Bit(3, 30, "信源ID") var sourceId: Long = 0L
    @Bit(4, 2, "备用") var spare: Int = 0
    @Bit(5, 30, "目的地ID") var destinationId: Long = 0L
    @Bit(6, 2, "备用2") var spare2: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        sourceId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        destinationId = reader.readBits(30).toLong()
        spare2 = reader.readBits(2)
    }
}

/**
 * 消息11：协调世界时/日期应答
 *
 * 参考消息4
 */
@UniqueId(11)
class AisMessage11: AisMessage4 {
    constructor()


    constructor(byteArray: ByteArray): super(byteArray)
}

/**
 * 消息12：寻址安全相关消息
 *
 * 寻址安全相关消息的长度可变，由安全相关文本的数量决定。长度应在1至5时隙间变化。
 */
@UniqueId(12)
class AisMessage12: AisMessage {
    @Bit(3, 2, "序列编号") var sequenceNumber: Int = 0
    @Bit(4, 30, "目的地ID") var destinationId: Long = 0L
    @Bit(5, 1, "重发标志") var retransmitFlag: Int = 0
    @Bit(6, 1, "备用") var spare: Int = 0
    @Bit(7, 0, "安全相关文本") var securityRelatedText: String = ""

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        sequenceNumber = reader.readBits(2)
        destinationId = reader.readBits(30).toLong()
        retransmitFlag = reader.readBits(1)
        spare = reader.readBits(1)
        securityRelatedText = reader.readBitsToByteArray(byteArray.size * 8 - 40).toString(Charsets.UTF_8)
    }
}

/**
 * 消息13：安全相关确认
 *
 * 参考消息7
 */
@UniqueId(13)
class AisMessage13: AisMessage7 {
    constructor()


    constructor(byteArray: ByteArray): super(byteArray)
}

/**
 * 消息14：安全相关广播消息
 *
 * 与安全有关的广播消息的长度根据与安全有关的文本的量应可以变化。该长度应在1到5个时隙之间变化。
 *
 * AIS-SART应使用消息14，安全相关文本应为：
 * 1) 对于现行的 SART，文本应为“SART ACTIVE”。
 * 2) 对于SART测试模式，文本应为“SART TEST”。
 * 3) 对于现行的MOB，文本应为“MOB ACTIVE”。
 * 4) 对于MOB测试模式，文本应为“MOB TEST”。
 * 5) 对于现行的EPIRB，文本应为“EPIRB ACTIVE”。
 * 6) 对于EPIRB测试模式，文本应为“EPIRB TEST”
 */
@UniqueId(14)
class AisMessage14: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 0, "安全相关文本") var securityRelatedText: String = ""

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        securityRelatedText = reader.readBitsToByteArray(byteArray.size * 8 - 40).toString(Charsets.UTF_8)
    }
}

/**
 * 消息15：询问
 *
 * 该消息不同于对UTC 和数据的请求，它应用于通过TDMA（不是DSC）VHF数据链路的询问。其响应在收到询问后应在该信道上发送。
 */
@UniqueId(15)
class AisMessage15: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 30, "目的地ID1") var destinationId1: Long = 0L
    @Bit(5, 6, "消息ID1.1") var messageId1: Int = 0
    @Bit(6, 10, "时隙偏置1.1") var slotOffset1: Int = 0
    @Bit(7, 2, "备用1") var spare1: Int = 0
    @Bit(8, 6, "消息ID1.2") var messageId2: Int = 0
    @Bit(9, 10, "时隙偏置1.2") var slotOffset2: Int = 0
    @Bit(10, 2, "备用2") var spare2: Int = 0
    @Bit(11, 30, "目的地ID2") var destinationId2: Long = 0L
    @Bit(12, 6, "消息ID2.1") var messageId3: Int = 0
    @Bit(13, 10, "时隙偏置2.1") var slotOffset3: Int = 0
    @Bit(14, 2, "备用3") var spare3: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        destinationId1 = reader.readBits(30).toLong()
        messageId1 = reader.readBits(6)
        slotOffset1 = reader.readBits(10)
        spare1 = reader.readBits(2)
        messageId2 = reader.readBits(6)
        slotOffset2 = reader.readBits(10)
        spare2 = reader.readBits(2)
        destinationId2 = reader.readBits(30).toLong()
        messageId3 = reader.readBits(6)
        slotOffset3 = reader.readBits(10)
        spare3 = reader.readBits(2)
    }
}

/**
 * 消息16：指配模式命令
 *
 * 指配命令由作为主控实体工作的基站发送。可以给其他台站指配一种与当前所用不同的发送计划。如果给某个台站指配了计划，它也进入指配模式。
 * 可以同时指配两个台站的计划。
 * 在接收一个指配计划时，台站可给该指配附加一个超时标记，可随机在第一次发送之后4至8 min间选择。
 * 当一个A类船载移动AIS台接收到一个指配，它应回复或者是指配的报告频次或者是作为结果的报告频次（当采用了时隙指配）或者是自主确定的报告频次（见附件2的第4.3.1节），取其中最大的。即使该A类船载移动AIS台回复了一个较高的自主确定的报告频次，它也应指出它所处的指配模式（通过采用适当的消息）。
 * 注1 – 指配台站应监测移动台的发送以确定移动台何时超时。
 * 对于指配设置的限制见附件2的表。
 * 由基站采用传输时隙指配传输的消息16应考虑将传输放在已经由基站的FATDMA（消息20）事先保留的时隙中。
 * 如果需要继续指配，在先前指配的最后时帧开始之前应发送新的指配。
 */
@UniqueId(16)
class AisMessage16: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 30, "目的地IDA") var destinationId1: Long = 0L
    @Bit(5, 12, "偏置A") var slotOffset1: Int = 0
    @Bit(6, 10, "增量A") var increment1: Int = 0
    @Bit(7, 30, "目的地IDB") var destinationId2: Long = 0L
    @Bit(8, 12, "偏置B") var slotOffset2: Int = 0
    @Bit(9, 10, "增量B") var increment2: Int = 0
    @Bit(10, 0, "备用2") var spare2: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        destinationId1 = reader.readBits(30).toLong()
        slotOffset1 = reader.readBits(12)
        increment1 = reader.readBits(10)
        destinationId2 = reader.readBits(30).toLong()
        slotOffset2 = reader.readBits(12)
        increment2 = reader.readBits(10)
        spare2 = reader.readBits(byteArray.size * 8 - 100)
    }
}

/**
 * 消息17：全球导航卫星系统广播二进制消息
 *
 * 该消息应由基站发送，该基站与DGNSS参考源相连，其设备配置适于向接收台站提供DGNSS数据。数据内容应符合ITU-R M.823建议书，但前置码和奇偶格式编排除外。
 */
@UniqueId(17)
class AisMessage17: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 18, "经度") var longitude: Long = 0L
    @Bit(5, 17, "纬度") var latitude: Long = 0L
    @Bit(6, 5, "备用2") var spare2: Int = 0
    @Bit(7, 0, "二进制数据") var binaryData: ByteArray = byteArrayOf() // 420 bits for binary data, adjust as needed

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        longitude = reader.readBits(18).toLong()
        latitude = reader.readBits(17).toLong()
        spare2 = reader.readBits(5)
        binaryData = reader.readBitsToByteArray(byteArray.size * 8 - 80)
    }
}

/**
 * 消息18：标准的B类设备位置报告
 *
 * 标准的B类设备位置报告应定期自主生成，而消息1、2或3只用于B类船载移动设备。报告间隔的默认值应为附件1的表2给出的值，除非接收16或23另行规定；并取决于当前的SOG和导航状态标志设置。
 */
@UniqueId(18)
class AisMessage18: AisMessage {
    @Bit(3, 8, "备用") var spare: Int = 0
    @Bit(4, 10, "对地航速") var sog: Int = 0
    @Bit(5, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(6, 28, "经度") var longitude: Long = 0L
    @Bit(7, 27, "纬度") var latitude: Long = 0L
    @Bit(8, 12, "对地航向") var cog: Int = 0
    @Bit(9, 9, "真航向") var heading: Int = 0
    @Bit(10, 6, "时间戳") var second: Int = 0
    @Bit(11, 4, "备用2") var spare2: Int = 0
    @Bit(12, 1, "B类装置标志") var classBDeviceFlag: Int = 0
    @Bit(13, 1, "B类显示器标志") var classBDisplayFlag: Int = 0
    @Bit(14, 1, "B类DSC标志") var classBDscFlag: Int = 0
    @Bit(15, 1, "B类带宽标志") var classBBandwidthFlag: Int = 0
    @Bit(16, 1, "B类消息22标志") var classBMessage22Flag: Int = 0
    @Bit(17, 1, "模式标志") var modeFlag: Int = 0
    @Bit(18, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(19, 1, "通信状态选择器标志") var commStateSelectorFlag: Int = 0
    @Bit(20, 2, "同步状态") var syncState: Int = 0
    @Bit(21, 3, "时隙超时") var slotTimeout: Int = 0
    @Bit(22, 14, "子消息") var subMessage: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(8)
        sog = reader.readBits(10)
        positionAccuracy = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        cog = reader.readBits(12)
        heading = reader.readBits(9)
        second = reader.readBits(6)
        spare2 = reader.readBits(4)
        classBDeviceFlag = reader.readBits(1)
        classBDisplayFlag = reader.readBits(1)
        classBDscFlag = reader.readBits(1)
        classBBandwidthFlag = reader.readBits(1)
        classBMessage22Flag = reader.readBits(1)
        modeFlag = reader.readBits(1)
        raimFlag = reader.readBits(1)
        commStateSelectorFlag = reader.readBits(1)
        syncState = reader.readBits(2)
        slotTimeout = reader.readBits(3)
        subMessage = reader.readBits(14)
    }
}

/**
 * 消息19：扩展的B类设备位置报告
 *
 *  对于未来设备：该消息并不需要且不应使用。所有内容由消息18、24A和24B涵盖。
 * 对于老设备：该消息应由B类船载移动设备采用。该消息应每6分钟发送一次，所用时隙是采用ITDMA通信状态下的消息18划分的两时隙。在下列参数值发生变化后应立即发送该消息：船舶尺寸/位置参考或电子定位装置的类型。
 */
@UniqueId(19)
class AisMessage19: AisMessage {
    @Bit(3, 8, "备用") var spare: Int = 0
    @Bit(4, 10, "对地航速") var sog: Int = 0
    @Bit(5, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(6, 28, "经度") var longitude: Long = 0L
    @Bit(7, 27, "纬度") var latitude: Long = 0L
    @Bit(8, 12, "对地航向") var cog: Int = 0
    @Bit(9, 9, "真航向") var heading: Int = 0
    @Bit(10, 6, "时间戳") var second: Int = 0
    @Bit(11, 4, "备用2") var spare2: Int = 0
    @Bit(12, 120, "名称", true) var name: String = ""
    @Bit(13, 8, "船舶类型") var shipType: Int = 0
    @Bit(14, 9, "船首尺寸") var toBow: Int = 0
    @Bit(15, 9, "船尾尺寸") var toStern: Int = 0
    @Bit(16, 6, "左舷尺寸") var toPort: Int = 0
    @Bit(17, 6, "右舷尺寸") var toStarboard: Int = 0
    @Bit(18, 4, "电子定位装置类型") var epfd: Int = 0
    @Bit(19, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(20, 1, "数据终端设备") var dte: Int = 0
    @Bit(21, 1, "同步状态") var syncState: Int = 0
    @Bit(22, 4, "备用3") var spare3: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(8)
        sog = reader.readBits(10)
        positionAccuracy = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        cog = reader.readBits(12)
        heading = reader.readBits(9)
        second = reader.readBits(6)
        spare2 = reader.readBits(4)
        name = buildString {
            for (i in 0 until 120 step 6) {
                var code = reader.readBits(6)
                if (code < 31)
                    code += 64
                append(code.toChar())
            }
        }
        shipType = reader.readBits(8)
        toBow = reader.readBits(9)
        toStern = reader.readBits(9)
        toPort = reader.readBits(6)
        toStarboard = reader.readBits(6)
        epfd = reader.readBits(4)
        raimFlag = reader.readBits(1)
        dte = reader.readBits(1)
        syncState = reader.readBits(1)
        spare3 = reader.readBits(4)
    }
}

/**
 * 消息20：数据链路管理消息
 *
 *  该消息应由基站用于提前公布一个或多个基站的固定指配计划（FATDMA），该消息应按照需要的次数重复。系统可借此为基站提供很高的完整性。这一点对于某些区域特别重要，在这些区域内有若干基站位置相邻，而移动台则在这些不同区域间漫游。移动台不能自主划分这些预留时隙。
 * 在发生超时前，在120海里内[28]移动台则应保留这些时隙，用于基站的发送。每发送一次消息20，基站应刷新一次超时值，以便移动台终止为基站使用时隙而做的保留（参考附件2的第3.3.1.2节）。
 * 偏置数目、时隙数目、超时和增量这几个参数应看做一个整体，如果规定了一个参数，该整体中的其他参数也应规定。偏置数目参数应指明从收到消息20的时隙到要保留的第一时隙之间的偏置。时隙数目参数应指明从要保留的第一个时隙算起的要保留的连续时隙数目。由此规定了一个预留码块。
 * 该预留码块应不超过5时隙。参数增量应指明各预留码块第一时隙间的时隙数目。增量为零表明每帧一个预留码块。建议用于增量的各值为：2，3，5，6，9，10，15，18，25，30，45，50，75，90，125，150，225，250，375，450，750或1125。使用这些值中的一个可保证在每帧内的对称时隙预留。该消息仅适用于发送该消息的频道。
 * 如果遇到询问情况且数据链路管理信息不可用，则仅应发送偏置数目1、时隙数目1、超时1和增量1。这些字段均应置为零。
 */
@UniqueId(20)
class AisMessage20: AisMessage {
    // 备用
    @Bit(3, 2, "备用") var spare: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
    }
}

/**
 * 消息21：助航设备报告
 *
 * 该消息应由助航（AtoN）AIS台使用。该台可以安装在助航设备上，或者在AtoN台站的功能集成到一个固定台站时，该消息也可以由该固定台站发送。该消息应以每三（3）分钟一次的频次发送，或通过VHF数据链路上的指配模式命令或通过按照外部命令指配报告频次。该消息占用的时隙应不超过两个。
 */
@UniqueId(21)
class AisMessage21: AisMessage {
    @Bit(3, 5, "助航设备类型") var aidType: Int = 0
    @Bit(4, 120, "助航设备名称", true) var aidName: String = ""
    @Bit(5, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(6, 28, "经度") var longitude: Long = 0L
    @Bit(7, 27, "纬度") var latitude: Long = 0L
    @Bit(8, 9, "船首尺寸") var toBow: Int = 0
    @Bit(9, 9, "船尾尺寸") var toStern: Int = 0
    @Bit(10, 6, "左舷尺寸") var toPort: Int = 0
    @Bit(11, 6, "右舷尺寸") var toStarboard: Int = 0
    @Bit(12, 4, "电子定位装置类型") var electronicPositionFixingDeviceType: Int = 0
    @Bit(13, 6, "时间戳") var second: Int = 0
    @Bit(14, 1, "偏置位置指示符") var offsetPositionIndicator: Int = 0
    @Bit(15, 8, "助航设备状态") var aidStatus: Int = 0
    @Bit(16, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(17, 1, "虚拟助航设备标志") var virtualAidFlag: Int = 0
    @Bit(18, 1, "指配模式标志") var assignedModeFlag: Int = 0
    @Bit(19, 1, "备用") var spare: Int = 0
    @Bit(20, 0, "助航设备名称扩展", true) var aidNameExtension: String = ""

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        aidType = reader.readBits(5)
        aidName = buildString {
            for (i in 0 until 120 step 6) {
                var code = reader.readBits(6)
                if (code < 31)
                    code += 64
                append(code.toChar())
            }
        }
        positionAccuracy = reader.readBits(1)
        longitude = reader.readBits(28).toLong()
        latitude = reader.readBits(27).toLong()
        toBow = reader.readBits(9)
        toStern = reader.readBits(9)
        toPort = reader.readBits(6)
        toStarboard = reader.readBits(6)
        electronicPositionFixingDeviceType = reader.readBits(4)
        second = reader.readBits(6)
        offsetPositionIndicator = reader.readBits(1)
        aidStatus = reader.readBits(8)
        raimFlag = reader.readBits(1)
        virtualAidFlag = reader.readBits(1)
        assignedModeFlag = reader.readBits(1)
        spare = reader.readBits(1)
//        aidNameExtension = buildString {
//            while (reader.remainingBits() >= 6) {
//                var code = reader.readBits(6)
//                if (code < 31)
//                    code += 64
//                append(code.toChar())
//            }
//        }
    }
}

/**
 * 消息22：信道管理
 *
 * 该消息应由基站（作为一个广播消息）发送，为该消息指配的地理地区给出VHF数据链路参数并应配有消息4传输，以在120海里内评估消息。由该消息指配的地理地区应符合附件2第4.1节的规定。另外，该消息也可由基站（作为寻址消息）使用，以命令单独的AIS移动台采用特定的VHF数据链路参数。如果遇到询问情况且被询问的基站没有采取信道管理措施，则应发送“不可用”和/或国际默认设置值（见附件2的第4.1节）。
 */
@UniqueId(22)
class AisMessage22: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 12, "信道A") var channelA: Int = 0
    @Bit(5, 12, "信道B") var channelB: Int = 0
    @Bit(6, 4, "Tx/Rx模式") var txRxMode: Int = 0
    @Bit(7, 1, "功率") var power: Int = 0
    @Bit(8, 18, "经度1") var longitude1: Long = 0L
    @Bit(9, 17, "纬度1") var latitude1: Long = 0L
    @Bit(10, 18, "经度2") var longitude2: Long = 0L
    @Bit(11, 17, "纬度2") var latitude2: Long = 0L
    @Bit(12, 1, "寻址或广播消息指示符") var addressingOrBroadcastIndicator: Int = 0
    @Bit(13, 1, "信道A带宽") var channelABandwidth: Int = 0
    @Bit(14, 1, "信道B带宽") var channelBBandwidth: Int = 0
    @Bit(15, 3, "切换区范围") var switchingAreaRange: Int = 0
    @Bit(16, 23, "备用2") var spare2: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        channelA = reader.readBits(12)
        channelB = reader.readBits(12)
        txRxMode = reader.readBits(4)
        power = reader.readBits(1)
        longitude1 = reader.readBits(18).toLong()
        latitude1 = reader.readBits(17).toLong()
        longitude2 = reader.readBits(18).toLong()
        latitude2 = reader.readBits(17).toLong()
        addressingOrBroadcastIndicator = reader.readBits(1)
        channelABandwidth = reader.readBits(1)
        channelBBandwidth = reader.readBits(1)
        switchingAreaRange = reader.readBits(3)
        spare2 = reader.readBits(23)
    }
}

/**
 * 消息23：群组指配命令
 *
 *  群组指配命令是当基站作为主控实体工作时由其发送的（见附件7的第*******.2节及本附件的第3.20节）。该消息在规定的区域内并通过“船只和货物类型”，或是通过“台站类型”选择适用于移动台。它控制着移动台的下述工作参数：
 * – 发送/接收模式；
 * – 报告时间；以及
 * – 寂静时间。
 * 应采用台站类型10来定义A类和B类“SO”移动站控制消息27传输的基站覆盖区。当台站类型为10时，仅适用纬度、经度字段，忽略所有其他字段。在从同一基站（相同MMSI）最后一次收到控制消息4之后的三分钟内，该信息是相关的。
 */
@UniqueId(23)
class AisMessage23: AisMessage {
    @Bit(3, 2, "备用") var spare: Int = 0
    @Bit(4, 18, "经度1") var longitude1: Long = 0L
    @Bit(5, 17, "纬度1") var latitude1: Long = 0L
    @Bit(6, 18, "经度2") var longitude2: Long = 0L
    @Bit(7, 17, "纬度2") var latitude2: Long = 0L
    @Bit(8, 4, "台站类型") var stationType: Int = 0
    @Bit(9, 8, "船舶类型和货物类型") var shipAndCargoType: Int = 0
    @Bit(10, 22, "备用2") var spare2: Int = 0
    @Bit(11, 2, "Tx/Rx模式") var txRxMode: Int = 0
    @Bit(12, 4, "报告间隔") var reportInterval: Int = 0
    @Bit(13, 4, "寂静时间") var quietTime: Int = 0
    @Bit(14, 6, "备用3") var spare3: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        spare = reader.readBits(2)
        longitude1 = reader.readBits(18).toLong()
        latitude1 = reader.readBits(17).toLong()
        longitude2 = reader.readBits(18).toLong()
        latitude2 = reader.readBits(17).toLong()
        stationType = reader.readBits(4)
        shipAndCargoType = reader.readBits(8)
        spare2 = reader.readBits(22)
        txRxMode = reader.readBits(2)
        reportInterval = reader.readBits(4)
        quietTime = reader.readBits(4)
        spare3 = reader.readBits(6)
    }
}

/**
 * 消息24：静态数据报告
 *
 * 支持消息24的A部分的设备须每6分钟变更频道传输一次。
 * 消息24的A部分可由任何AIS台用于将某个名称与MMSI关联。
 * 消息24的A部分和B部分应由B类“CS”和B类“SO”船载移动设备每六分钟发送一次。该消息由两部分组成。消息24B应在消息24A之后的1 min内发送。
 * 当用于电子定位装置的类型的船舶尺寸/位置参考参数值发生变化时，B类“CS”和B类“SO”应传输消息24B。
 * 当要求B类“CS”或B类“SO”传输消息24时，AIS台应以A部分和B部分响应。
 * 当要求A类传输消息24时，AIS台应以B部分响应，该部分可能只包括供应商ID。
 */
@UniqueId(24)
open class AisMessage24: AisMessage {
    @Bit(3, 2, "部分编号") var partNumber: Int = 0
    @Bit(4, 120, "名称", true) var name: String = ""
    @Bit(0, 8, "船舶类型和货物类型") var shipAndCargoType: Int = 0
    @Bit(1, 42, "供应商ID", true) var vendorId: String = ""
    @Bit(2, 42, "呼号", true) var callSign: String = ""
    @Bit(3, 9, "船首尺寸") var toBow: Int = 0
    @Bit(4, 9, "船尾尺寸") var toStern: Int = 0
    @Bit(5, 6, "左舷尺寸") var toPort: Int = 0
    @Bit(6, 6, "右舷尺寸") var toStarboard: Int = 0
    @Bit(7, 4, "电子定位装置类型") var electronicPositionFixingDeviceType: Int = 0
    @Bit(8, 2, "备用") var spare: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        partNumber = reader.readBits(2)

        if (partNumber == 0) {
            name = buildString {
                for (i in 0 until 120 step 6) {
                    var code = reader.readBits(6)
                    if (code < 31)
                        code += 64
                    append(code.toChar())
                }
            }
        } else {
            shipAndCargoType = reader.readBits(8)
            vendorId = buildString {
                for (i in 0 until 42 step 6) {
                    var code = reader.readBits(6)
                    if (code < 31)
                        code += 64
                    append(code.toChar())
                }
            }
            callSign = buildString {
                for (i in 0 until 42 step 6) {
                    var code = reader.readBits(6)
                    if (code < 31)
                        code += 64
                    append(code.toChar())
                }
            }
            toBow = reader.readBits(9)
            toStern = reader.readBits(9)
            toPort = reader.readBits(6)
            toStarboard = reader.readBits(6)
            electronicPositionFixingDeviceType = reader.readBits(4)
            spare = reader.readBits(2)
        }
    }
}

/**
 * 消息25：单时隙二进制消息
 *
 * 该消息主要用于简短而且少量的数据传输。单时隙二进制消息根据其内容的编码方法及广播或寻址的目的地指示，可容纳最多128的数据比特。长度不应超过一个时隙。见附件5的第2.1节中的应用标识符。
 * 该消息不应通过消息7或13来确认。
 */
@UniqueId(25)
class AisMessage25: AisMessage {
    @Bit(3, 1, "目的地ID指示符") var destinationIdIndicator: Int = 0
    @Bit(4, 1, "二进制数据标记") var binaryDataFlag: Int = 0
    @Bit(5, 30, "目的地ID") @Indicator("destinationIdIndicator") var destinationId: Long = 0L
    @Bit(6, 2, "备用") @Indicator("destinationIdIndicator") var spare: Int = 0
    @Bit(7, 0, "二进制数据") var binaryData: ByteArray = byteArrayOf()

    constructor()

    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
//        destinationIdIndicator = reader.readBits(1)
//        binaryDataFlag = reader.readBits(1)
//        destinationId = reader.readBits(30).toLong()
//        spare = reader.readBits(2)
//        binaryData = reader.readBits(128).toString(2)
    }
}

/**
 * 消息26：带有通信状态的多时隙二进制消息
 *
 * 该消息的主要目的是通过应用SOTDMA或ITDMA接入方式预定二进制数据传输。该多时隙二进制消息取决于对内容的编码方法以及广播或寻址的目的地指示，可包含最多1 004个数据比特（使用5个时隙）。见附件5的第2.1节中的应用标识符。
 * 该消息不应通过消息息7或13来确认。
 */
@UniqueId(26)
class AisMessage26: AisMessage {
    @Bit(3, 1, "目的地ID指示符") var destinationIdIndicator: Int = 0
    @Bit(4, 1, "二进制数据标记") var binaryDataFlag: Int = 0
    @Bit(5, 30, "目的地ID") @Indicator("destinationIdIndicator") var destinationId: Long = 0L
    @Bit(6, 2, "备用") @Indicator("destinationIdIndicator") var spare: Int = 0
    @Bit(7, 0, "二进制数据") var binaryData: String = ""

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
//        destinationIdIndicator = reader.readBits(1)
//        binaryDataFlag = reader.readBits(1)
//        destinationId = reader.readBits(30).toLong()
//        spare = reader.readBits(2)
//        binaryData = reader.readBits(1004).toString(2)
    }
}

/**
 * 消息27：远距离自动识别系统广播消息
 *
 * 此消息主要是用于远距离的AIS A类和B类“SO”装备的船只的检测（通常由卫星进行）。此消息具有与消息1、2和3类似的内容，但比特总数已被压缩，以便增加与远距离检测相关的传播延迟。关于远距离应用的详细内容参见附件4。
 */
@UniqueId(27)
class AisMessage27: AisMessage {
    @Bit(3, 1, "位置准确度") var positionAccuracy: Int = 0
    @Bit(4, 1, "RAIM标志") var raimFlag: Int = 0
    @Bit(5, 4, "航行状态") var navigationalStatus: Int = 0
    @Bit(6, 18, "经度") var longitude: Long = 0L
    @Bit(7, 17, "纬度") var latitude: Long = 0L
    @Bit(8, 6, "对地航速") var speedOverGround: Int = 0
    @Bit(9, 9, "对地航向") var courseOverGround: Int = 0
    @Bit(10, 1, "位置等待时间") var positionWaitTime: Int = 0
    @Bit(11, 1, "备用") var spare: Int = 0

    constructor()


    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        msgId = reader.readBits(6)
        repeat = reader.readBits(2)
        userId = reader.readBits(30).toLong()
        positionAccuracy = reader.readBits(1)
        raimFlag = reader.readBits(1)
        navigationalStatus = reader.readBits(4)
        longitude = reader.readBits(18).toLong()
        latitude = reader.readBits(17).toLong()
        speedOverGround = reader.readBits(6)
        courseOverGround = reader.readBits(9)
        positionWaitTime = reader.readBits(1)
        spare = reader.readBits(1)
    }
}