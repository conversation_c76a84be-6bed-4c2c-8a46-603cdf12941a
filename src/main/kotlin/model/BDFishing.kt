package org.example.model

import java.io.Serializable

class BDFishingData : Serializable {
    // 静态数据
    var id: String = ""
    var card: String = ""
    var time: String = ""
    var mmsi: String = ""
    var eid: String = ""
    var imo: String = ""
    var callSign: String = ""
    var name: String = ""
    var type: String = ""
    var loa: String = ""
    var bm: String = ""
    var eta: String = ""
    var draught: String = ""
    var dest: String = ""
    var epfs: String = ""
    var mid: String = ""
    var umc: String = ""
    var sn: String = ""
    var fromType: String = ""

    // 动态数据
    var state: String = ""
    var rot: String = ""
    var sog: String = ""
    var lng: String = ""
    var lat: String = ""
    var cog: String = ""
    var heading: String = ""
    var timestamp: String = ""
}
