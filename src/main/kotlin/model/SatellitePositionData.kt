package org.example.model

import java.io.Serializable

class SatellitePositionData : Serializable {
    var date: String = ""
    var mmsi: String = ""
    var code: Int = 0
    var turn: Int = 0
    var speed: Double = 0.0
    var accuracy: Int = 0
    var geohash: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
    var course: Double = 0.0
    var heading: Int = 0
}

/**
 * 1-dtype	2-CommType	3-ID	4-ReceiveTime	5-IMO	6-Callsign	7-ShipName
 * 数据类型	通信类型id	设备唯一标识	接收时间	IMO编号	呼号	船名
 * 固定为：2	1: AIS	MMSI：数字	utc秒	数字	英文字符	英文字符
 * 8-ShipType	9-Length	10-Breadth	11-ETA	12-Draught	13-Destination	14-SourceId
 * AIS船舶类型ID	船长	船宽	预计到达时间	吃水	目的港	数据来源
 * 参考GBT20068-2006
 * 表27：船舶类型	单位：m	单位：m	格式：MM-DD HH:MM
 * 月-日 小时:分钟	单位：0.1m	英文字符	1：卫星
 * 15-LengthBow	16-LengthStern	17-BreadthPort	18-BreadthStarboard
 */
class SatelliteShipData : Serializable {
    var date: String = ""
    var mmsi: String = ""
    var imo: String = ""
    var callSign: String = ""
    var name: String = ""
    var type: Int = 0
    var loa: Int = 0
    var bm: Int = 0
    var eta: String = ""
    var draught: Double = 0.0
    var dest: String = ""
    var lengthBow: Int = 0
    var lengthStern: Int = 0
    var breadthPort: Int = 0
    var breadthStarboard: Int = 0
}