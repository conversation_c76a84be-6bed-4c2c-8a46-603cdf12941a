package org.example.kapt

import org.example.datasource.msa.guangxi.GXFishingData
import org.example.model.BDFishingData
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface BDFishingMapper {

    fun toBDFishingData(data: GXFishingData): BDFishingData

    companion object {
        val INSTANCE: BDFishingMapper = Mappers.getMapper(BDFishingMapper::class.java)
    }
}