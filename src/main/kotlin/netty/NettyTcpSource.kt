package org.example.netty

import org.apache.flink.api.common.typeutils.base.VoidSerializer
import org.apache.flink.api.connector.source.Source
import org.apache.flink.api.connector.source.SourceReader
import org.apache.flink.api.connector.source.SplitEnumerator
import org.apache.flink.api.connector.source.SplitEnumeratorContext
import org.apache.flink.core.io.SimpleVersionedSerializer
import org.apache.flink.api.connector.source.Boundedness
import org.apache.flink.api.connector.source.SourceReaderContext

class NettyTcpSource(
    private val tcpConnections: List<NettyClientConfig>
) : Source<String, NettySplit, Void> {

    override fun createReader(context: SourceReaderContext): SourceReader<String, NettySplit> {
        return NettySourceReader(context)
    }

    override fun createEnumerator(enumContext: SplitEnumeratorContext<NettySplit>): SplitEnumerator<NettySplit, Void> {
        return NettySplitEnumerator(enumContext, tcpConnections)
    }

    override fun restoreEnumerator(
        enumContext: SplitEnumeratorContext<NettySplit>,
        checkpoint: Void
    ): SplitEnumerator<NettySplit, Void> {
        // 无状态，不需要从 checkpoint 恢复，直接 new
        return NettySplitEnumerator(enumContext, tcpConnections)
    }

    override fun getSplitSerializer(): SimpleVersionedSerializer<NettySplit> {
        return NettySplitSerializer()
    }

    override fun getEnumeratorCheckpointSerializer(): SimpleVersionedSerializer<Void> {
        return NoOpSerializer
    }

    override fun getBoundedness(): Boundedness {
        return Boundedness.CONTINUOUS_UNBOUNDED
    }
}

