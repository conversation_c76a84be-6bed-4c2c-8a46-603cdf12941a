package org.example.netty

import org.apache.flink.api.connector.source.SourceSplit
import java.io.Serializable

/**
 * 代表一个 TCP 连接信息（IP+端口等）
 */
data class NettySplit(val host: String, val port: Int, val splitId: String, val username: String = "", val password: String = "") : SourceSplit, Serializable {
    override fun splitId(): String = splitId
}

data class NettyClientConfig(
    val host: String, val port: Int, val username: String = "", val password: String = ""
) : Serializable