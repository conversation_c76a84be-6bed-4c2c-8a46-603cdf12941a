package org.example.netty

import org.apache.flink.core.io.SimpleVersionedSerializer
import java.nio.ByteBuffer

class NettySplitSerializer : SimpleVersionedSerializer<NettySplit> {

    override fun getVersion(): Int = 1

    override fun serialize(split: NettySplit): ByteArray {
        val hostBytes = split.host.toByteArray(Charsets.UTF_8)
        val splitIdBytes = split.splitId.toByteArray(Charsets.UTF_8)
        val usernameBytes = split.username.toByteArray(Charsets.UTF_8)
        val passwordBytes = split.password.toByteArray(Charsets.UTF_8)

        val totalSize = 4 + hostBytes.size +
                4 + splitIdBytes.size +
                4 + usernameBytes.size +
                4 + passwordBytes.size +
                4 // port

        val buffer = ByteBuffer.allocate(totalSize)

        putString(buffer, hostBytes)
        putString(buffer, splitIdBytes)
        putString(buffer, usernameBytes)
        putString(buffer, passwordBytes)
        buffer.putInt(split.port)

        return buffer.array()
    }

    override fun deserialize(version: Int, serialized: ByteArray): NettySplit {
        val buffer = ByteBuffer.wrap(serialized)

        val host = getString(buffer)
        val splitId = getString(buffer)
        val username = getString(buffer)
        val password = getString(buffer)
        val port = buffer.int

        return NettySplit(host, port, splitId, username, password)
    }

    private fun putString(buffer: ByteBuffer, bytes: ByteArray) {
        buffer.putInt(bytes.size)
        buffer.put(bytes)
    }

    private fun getString(buffer: ByteBuffer): String {
        val size = buffer.int
        val bytes = ByteArray(size)
        buffer.get(bytes)
        return String(bytes, Charsets.UTF_8)
    }
}

