package org.example.netty

import org.apache.flink.api.connector.source.SplitEnumerator
import org.apache.flink.api.connector.source.SplitEnumeratorContext

/**
 * 管理所有 TCP 连接 Split，分配给读任务
 */
class NettySplitEnumerator(
    private val context: SplitEnumeratorContext<NettySplit>,
    private val tcpConnections: List<NettyClientConfig> // List of (host, port)
) : SplitEnumerator<NettySplit, Void> {

    private val remainingSplits = tcpConnections.mapIndexed { idx, config ->
        NettySplit(config.host, config.port, "tcp-split-$idx", config.username, config.password)
    }.toMutableList()

    private var nextSubtaskIndex = 0

    override fun start() {
        // 启动时向所有任务分配 Split
        assignSplits()
    }

    override fun handleSplitRequest(subtaskId: Int, requesterHostname: String?) {
        assignSplits()
    }

    private fun assignSplits() {
        val registeredReaders = context.registeredReaders().keys.toList()
        if (registeredReaders.isEmpty()) return

        while (remainingSplits.isNotEmpty()) {
            val subtaskId = registeredReaders[nextSubtaskIndex % registeredReaders.size]
            val split = remainingSplits.removeAt(0)
            context.assignSplit(split, subtaskId)
            nextSubtaskIndex++
        }
    }


    override fun addSplitsBack(splits: List<NettySplit>, subtaskId: Int) {
        remainingSplits.addAll(splits)
    }

    override fun addReader(subtaskId: Int) {
        // 当有新的 subtask 注册时，尝试为它分配 split
        assignSplits()
    }


    override fun snapshotState(l: Long): Void? = null

    override fun close() {}
}
