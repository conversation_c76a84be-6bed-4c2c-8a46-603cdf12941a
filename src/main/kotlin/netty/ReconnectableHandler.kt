package org.example.netty

import io.netty.channel.ChannelHandlerContext
import io.netty.channel.SimpleChannelInboundHandler
import org.example.cache.NettyCache
import org.example.netty.NettyUtil.generateLoginByteBuf
import org.example.util.CommonUtil.log
import java.util.concurrent.TimeUnit

class ReconnectableHandler(
    private val split: NettySplit,
    private val onMessage: (ctx: ChannelHandlerContext?, String?) -> Unit,
    private val reconnectAction: (NettySplit) -> Unit
) : SimpleChannelInboundHandler<String>() {
    override fun channelActive(ctx: ChannelHandlerContext) {
        val buffer = generateLoginByteBuf(split.username, split.password)
        log.info("连接成功: ${split.host}:${split.port}，发送登录数据")
        if (split.username.isNotEmpty() && split.password.isNotEmpty()) {
            ctx.writeAndFlush(buffer)
        }
    }

    override fun channelRead0(ctx: ChannelHandlerContext?, msg: String?) {
        onMessage(ctx, msg)
    }

    override fun channelInactive(ctx: ChannelHandlerContext) {
        val retryCount = NettyCache.getCache("${split.host}_${split.port}_retryCount")?.toIntOrNull() ?: 0
        log.warn("连接断开: ${split.host}:${split.port}，准备${5L shl retryCount}秒后重连")
        // 重连时间指数增长
        ctx.channel().eventLoop().schedule({
            reconnectAction(split)
        }, 5L shl retryCount, TimeUnit.SECONDS)
        NettyCache.setCache("${split.host}_${split.port}_retryCount", (retryCount + 1).toString())
    }

    override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
        log.error("连接异常: ${cause.message}", cause)
        ctx.close()
    }
}
