package org.example.netty

import org.apache.flink.api.connector.source.SourceReader
import org.apache.flink.api.connector.source.SourceReaderContext
import org.apache.flink.api.connector.source.ReaderOutput
import io.netty.bootstrap.Bootstrap
import io.netty.channel.*
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioSocketChannel
import io.netty.handler.codec.LineBasedFrameDecoder
import io.netty.handler.codec.string.StringDecoder
import org.apache.flink.api.connector.source.SourceEvent
import org.apache.flink.core.io.InputStatus
import org.example.cache.NettyCache
import org.example.netty.NettyUtil.generateLoginByteBuf
import org.example.netty.NettyUtil.generateMessageByteBuf
import org.example.util.CommonUtil.log
import org.example.util.aesDecrypt
import java.util.concurrent.TimeUnit
import kotlin.math.log

/**
 * 实际启动 Netty 客户端连接 TCP，读取数据
 */
class NettySourceReader(
    private val context: SourceReaderContext
) : SourceReader<String, NettySplit> {

    private val activeChannels = mutableMapOf<NettySplit, Channel>()
    private val eventLoopGroup = NioEventLoopGroup()
    private val splitBuffers = mutableMapOf<NettySplit, MutableList<String>>()
    private val loginStatusMap = mutableMapOf<NettySplit, Boolean>()
    private val ivMap = mutableMapOf<NettySplit, String>()

    override fun start(): Unit = Unit

    override fun pollNext(output: ReaderOutput<String>): InputStatus {
        synchronized(splitBuffers) {
            for ((split, buffer) in splitBuffers) {
                while (buffer.isNotEmpty()) {
                    val record = buffer.removeAt(0)
                    output.collect(record)
                }
            }
        }
        return InputStatus.MORE_AVAILABLE // 告诉 Flink 我们一直有数据
    }

    override fun addSplits(splits: MutableList<NettySplit>) {
        splits.forEach { split ->
            connectTcp(split)
        }
    }


    private fun connectTcp(split: NettySplit) {
        splitBuffers[split] = mutableListOf()
        loginStatusMap[split] = split.username.isNotEmpty() && split.password.isNotEmpty()

        val bootstrap = when (split.username) {
            "TjBeiBao" -> getSatelliteBootstrap(split)
            else -> getAisBootstrap(split)
        }

        val future = bootstrap.connect(split.host, split.port)
        future.addListener { f ->
            val retryCount = NettyCache.getCache("${split.host}_${split.port}_retryCount")?.toIntOrNull() ?: 0
            if (!f.isSuccess) {
                log.error("连接失败，${split.host}:${split.port} 第 ${retryCount + 1} 次尝试，准备${5L shl retryCount}秒后重连")
                scheduleReconnect(split, 5L shl retryCount)
                NettyCache.setCache("${split.host}_${split.port}_retryCount", (retryCount + 1).toString())
            } else {
                log.info("连接成功 ${split.host}:${split.port}")
                val oldChannel = activeChannels[split]
                if (oldChannel != null && oldChannel.isOpen) {
                    oldChannel.close()
                }
                NettyConnectionManager.channels.removeIf { it.first == split }

                activeChannels[split] = future.channel()
                NettyConnectionManager.channels.add(Pair(split, future.channel()))

            }
        }
    }

    private fun getAisBootstrap(split: NettySplit) = Bootstrap()
        .group(eventLoopGroup)
        .channel(NioSocketChannel::class.java)
        .handler(object : ChannelInitializer<SocketChannel>() {
            override fun initChannel(ch: SocketChannel) {
                ch.pipeline().addLast("lineDecoder", LineBasedFrameDecoder(1024))
                ch.pipeline().addLast(StringDecoder())
                ch.pipeline().addLast(
                    ReconnectableHandler(
                        split = split,
                        onMessage = { ctx, msg ->
                            log.info("received: $msg")
                            splitBuffers[split]?.add(msg ?: "")
                        },
                        reconnectAction = { split ->
                            connectTcp(split)
                        }
                    )
                )
                ch.pipeline().addLast()
            }
        })

    private fun getSatelliteBootstrap(split: NettySplit) = Bootstrap()
        .group(eventLoopGroup)
        .channel(NioSocketChannel::class.java)
        .handler(object : ChannelInitializer<SocketChannel>() {
            override fun initChannel(ch: SocketChannel) {
                ch.pipeline().addLast("lineDecoder", LineBasedFrameDecoder(1024))
                ch.pipeline().addLast(StringDecoder())
                ch.pipeline().addLast(
                    ReconnectableHandler(
                        split = split,
                        onMessage = { ctx, msg ->
                            msg?.trimEnd('\r', '\n')?.split(" ")?.let { token ->
                                println(token)
                                val command = token.elementAtOrNull(0) ?: ""
                                if (loginStatusMap[split] == true) {
                                    if (command == "d") {
                                        synchronized(splitBuffers) {
//                                                splitBuffers[split]?.add(message.split(" ", limit = 3).last())
                                            val plainText = aesDecrypt(token.lastOrNull() ?: "", "3f8e7b2c5d1a4f6e9b0c2d3e4f5a6b7c", ivMap.getOrDefault(split, "")) ?: ""
                                            if (plainText.startsWith("\"") && plainText.endsWith("\"") && plainText.length >= 2) {
                                                splitBuffers[split]?.add(plainText.substring(1, plainText.length - 1))
                                            } else {
                                                splitBuffers[split]?.add(plainText)
                                            }
                                        }
                                    }
                                } else {
                                    if (command == "z") {
                                        val iv = token.lastOrNull() ?: ""
                                        val reply = "rz $iv"
                                        ivMap[split] = iv
                                        log.info("Received message: $token, reply: $reply")
                                        ctx?.writeAndFlush(generateMessageByteBuf("$reply\r\n"))
                                    } else if (command == "az") {
                                        log.info("Received message: $token, login user: ${split.username}")
                                        ctx?.writeAndFlush(generateMessageByteBuf("u ${split.username}\r\n"))
                                    } else if (command == "ru") {
                                        val loginCode = token.lastOrNull() ?: ""
                                        log.info("Received message: $token, login code: $loginCode")
                                        if (loginCode == "0") {
                                            loginStatusMap[split] = true
                                            log.info("Login success: ${split.host}:${split.port}")
                                            ctx?.writeAndFlush(generateMessageByteBuf("au\r\n"))
                                        }
                                    }
                                }
                            }
                        },
                        reconnectAction = { split ->
                            connectTcp(split)
                        }
                    )
                )
                ch.pipeline().addLast()
            }
        })

    private fun scheduleReconnect(split: NettySplit, delay: Long = 5) {
        eventLoopGroup.schedule({
            connectTcp(split)
        }, delay, TimeUnit.SECONDS)
    }

    override fun snapshotState(l: Long) = listOf<NettySplit>()

    override fun notifyNoMoreSplits() {}

    override fun close() {
        activeChannels.forEach { (split, channel) ->
            if (channel.isActive) {
                log.info("Sending disconnect to ${split.host}:${split.port}")
                // 1. 发送退出通知
                channel.writeAndFlush(generateMessageByteBuf("x ${split.username}\r\n"))
                    .addListener(ChannelFutureListener.CLOSE)
            } else {
                channel.close()
            }
        }

        // 2. 优雅关闭 EventLoopGroup
        eventLoopGroup.shutdownGracefully().sync()
        log.info("NettySourceReader closed")
    }

    override fun handleSourceEvents(sourceEvent: SourceEvent?) {
        super.handleSourceEvents(sourceEvent)
    }

    override fun isAvailable() = null
}