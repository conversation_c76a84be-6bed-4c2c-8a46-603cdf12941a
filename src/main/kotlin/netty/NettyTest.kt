package org.example.netty

import ch.hsr.geohash.GeoHash
import io.netty.buffer.Unpooled
import io.netty.channel.Channel
import io.netty.channel.ChannelFutureListener
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.util.CharsetUtil
import org.apache.flink.api.common.eventtime.WatermarkStrategy
import org.apache.flink.connector.jdbc.JdbcConnectionOptions
import org.apache.flink.connector.jdbc.JdbcExecutionOptions
import org.apache.flink.connector.jdbc.JdbcSink
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.streaming.api.functions.ProcessFunction
import org.apache.flink.streaming.api.functions.sink.SinkFunction
import org.apache.flink.util.OutputTag
import org.example.model.SatellitePositionData
import org.example.model.SatelliteShipData
import org.example.netty.NettyUtil.generateMessageByteBuf
import org.example.util.CommonUtil.localDateTimeFormatter
import org.example.util.CommonUtil.log
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

object NettyConnectionManager {
    val channels = mutableListOf<Pair<NettySplit, Channel>>()
    val eventLoopGroup = NioEventLoopGroup()

    fun shutdown() {
        println("Shutting down NettyConnectionManager...")
        channels.forEach { (split, channel) ->
            if (channel.isActive) {
                channel.writeAndFlush(generateMessageByteBuf("x ${split.username}\r\n"))
                    .addListener(ChannelFutureListener.CLOSE)
            }
        }
        eventLoopGroup.shutdownGracefully()
    }
}

class LoggingJdbcSinkWrapper(
    private val delegate: SinkFunction<SatellitePositionData>
) : SinkFunction<SatellitePositionData> {

    private var counter = 0

    @Synchronized
    override fun invoke(value: SatellitePositionData, context: SinkFunction.Context) {
        delegate.invoke(value, context)
        counter++
        if (counter % 100 == 0) { // 这里每100条输出一次，可自定义
            log.info("已保存数据条数：$counter")
        }
    }
}


fun main() {
//    val clickhouseAddress = "**************:8123"
    val clickhouseAddress = "************:8123"
    val tcpConnections = listOf(
//        NettyClientConfig("*************", 9899, "TjBeiBao", "")
        NettyClientConfig("*************", 9899, "TjBeiBao", "")
    )

    val env = StreamExecutionEnvironment.getExecutionEnvironment()
    val nettySource = NettyTcpSource(tcpConnections)

    val positionSink: SinkFunction<SatellitePositionData> = JdbcSink.sink(
        """
            INSERT INTO `default`.ais_wxpositiontest_all
            (`date`, mmsi, code, turn, speed, accuracy, geohash, lat, lng, course, heading)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimIndent(),
        { ps, data ->
            ps.setString(1, data.date)
            ps.setString(2, data.mmsi)
            ps.setInt(3, data.code)
            ps.setInt(4, data.turn)
            ps.setFloat(5, data.speed.toFloat())
            ps.setInt(6, data.accuracy)
            ps.setString(7, data.geohash)
            ps.setFloat(8, data.lat.toFloat())
            ps.setFloat(9, data.lng.toFloat())
            ps.setFloat(10, data.course.toFloat())
            ps.setInt(11, data.heading)
        },
        JdbcExecutionOptions.builder()
            .withBatchIntervalMs(5000)   // 每5秒写一次
            .withMaxRetries(3)
            .build(),
        JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
            .withUrl("********************************************")
            .withDriverName("com.clickhouse.jdbc.ClickHouseDriver")
            .withUsername("default")
            .withPassword("123456")
            .build()
    )

    val shipSink: SinkFunction<SatelliteShipData> = JdbcSink.sink(
        """
            INSERT INTO `default`.ais_satellite_ship_all
            (`date`, mmsi, imo, callSign, name, type, loa, bm, eta, draught, dest, lengthBow, lengthStern, breadthPort, breadthStarboard)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimIndent(),
        { ps, data ->
            ps.setString(1, data.date)
            ps.setString(2, data.mmsi)
            ps.setString(3, data.imo)
            ps.setString(4, data.callSign)
            ps.setString(5, data.name)
            ps.setInt(6, data.type)
            ps.setInt(7, data.loa)
            ps.setInt(8, data.bm)
            ps.setString(9, data.eta)
            ps.setFloat(10, data.draught.toFloat())
            ps.setString(11, data.dest)
            ps.setInt(12, data.lengthBow)
            ps.setInt(13, data.lengthStern)
            ps.setInt(14, data.breadthPort)
            ps.setInt(15, data.breadthStarboard)
        },
        JdbcExecutionOptions.builder()
            .withBatchIntervalMs(5000)   // 每5秒写一次
            .withMaxRetries(3)
            .build(),
        JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
            .withUrl("********************************************")
            .withDriverName("com.clickhouse.jdbc.ClickHouseDriver")
            .withUsername("default")
            .withPassword("123456")
            .build()
    )

    val stream = env.fromSource(nettySource, WatermarkStrategy.noWatermarks(), "netty-tcp-source")

    // 定义侧输出标签
    val positionOutput = object : OutputTag<SatellitePositionData>("type-position") {}
    val shipOutput = object : OutputTag<SatelliteShipData>("type-ship") {}

    val processedStream = stream.process(object : ProcessFunction<String, List<String>>() {
        override fun processElement(
            value: String,
            ctx: ProcessFunction<String, List<String>>.Context,
            out: org.apache.flink.util.Collector<List<String>>
        ) {
            val parts = value.split("~")
//            println("接收到数据: $parts")
            when (parts[0]) {
                // dtype~CommType~ID~ReceiveTime~PosTime~Lon~Lat~Cog~Sog~TrueHeading~NavigationStatus~ROT~SourceId~PosAccuracy
                "1" -> ctx.output(positionOutput, SatellitePositionData().apply {
                    date = localDateTimeFormatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(parts.elementAtOrNull(4)?.toLongOrNull() ?: 0L), ZoneId.systemDefault()))
                    mmsi = parts.elementAtOrNull(2) ?: ""
                    code = parts.elementAtOrNull(10)?.toIntOrNull() ?: 0
                    turn = parts.elementAtOrNull(11)?.toIntOrNull() ?: 0
                    speed = (parts.elementAtOrNull(8)?.toDoubleOrNull() ?: 0.0) * 0.1
                    accuracy = parts.elementAtOrNull(13)?.toIntOrNull() ?: 0
                    lat = (parts.elementAtOrNull(6)?.toDoubleOrNull() ?: 0.0) / 600000.0
                    lng = (parts.elementAtOrNull(5)?.toDoubleOrNull() ?: 0.0) / 600000.0
                    geohash = GeoHash.withCharacterPrecision(lat, lng, 7).toBase32()
                    course = (parts.elementAtOrNull(7)?.toDoubleOrNull() ?: 0.0) * 0.1
                    heading = parts.elementAtOrNull(9)?.toIntOrNull() ?: 0
                })
                // dtype~CommType~ID~ReceiveTime~IMO~Callsign~ShipName~ShipType~Length~Breadth~ETA~Draught~Destination~SourceId~LengthBow~LengthStern~BreadthPort~BreadthStarboard
                "2" -> ctx.output(shipOutput, SatelliteShipData().apply {
                    date = localDateTimeFormatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(parts.elementAtOrNull(3)?.toLongOrNull() ?: 0L), ZoneId.systemDefault()))
                    mmsi = parts.elementAtOrNull(2) ?: ""
                    imo = parts.elementAtOrNull(4) ?: ""
                    callSign = parts.elementAtOrNull(5) ?: ""
                    name = parts.elementAtOrNull(6) ?: ""
                    type = parts.elementAtOrNull(7)?.toIntOrNull() ?: 0
                    loa = parts.elementAtOrNull(8)?.toIntOrNull() ?: 0
                    bm = parts.elementAtOrNull(9)?.toIntOrNull() ?: 0
                    eta = parts.elementAtOrNull(10) ?: ""
                    draught = (parts.elementAtOrNull(11)?.toDoubleOrNull() ?: 0.0) * 0.1
                    dest = parts.elementAtOrNull(12) ?: ""
                    lengthBow = parts.elementAtOrNull(14)?.toIntOrNull() ?: 0
                    lengthStern = parts.elementAtOrNull(15)?.toIntOrNull() ?: 0
                    breadthPort = parts.elementAtOrNull(16)?.toIntOrNull() ?: 0
                    breadthStarboard = parts.elementAtOrNull(17)?.toIntOrNull() ?: 0
                })
                else -> {}
            }
        }
    })

    // 取出各类型的数据流
    val positionStream = processedStream.getSideOutput(positionOutput)
    val shipStream = processedStream.getSideOutput(shipOutput)

    // stream.sinkTo(sink2)
    positionStream.addSink(positionSink)
    shipStream.addSink(shipSink)

    Runtime.getRuntime().addShutdownHook(Thread {
        NettyConnectionManager.shutdown()
    })

    env.execute()
}