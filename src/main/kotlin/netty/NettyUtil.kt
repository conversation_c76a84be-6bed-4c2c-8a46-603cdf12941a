package org.example.netty

import io.netty.buffer.ByteBuf
import io.netty.buffer.Unpooled
import java.nio.charset.StandardCharsets

object NettyUtil {
    fun generateLoginByteBuf(username: String, password: String): ByteBuf {
        val buffer: ByteBuf = Unpooled.buffer()
        buffer.writeByte(0x01)
        buffer.writeBytes(username.toByteArray(StandardCharsets.UTF_8))
        buffer.writeByte(0x00)
        buffer.writeBytes(password.toByteArray(StandardCharsets.UTF_8))
        buffer.writeByte(0x00)
        return buffer
    }

    fun generateMessageByteBuf(message: String): ByteBuf {
        val buffer: ByteBuf = Unpooled.buffer()
        buffer.writeBytes(message.toByteArray(StandardCharsets.UTF_8))
        return buffer
    }
}