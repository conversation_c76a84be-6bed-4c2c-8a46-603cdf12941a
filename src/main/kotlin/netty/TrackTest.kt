package org.example.netty

import ch.hsr.geohash.GeoHash
import org.apache.flink.api.common.eventtime.WatermarkStrategy
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.streaming.api.functions.ProcessFunction
import org.apache.flink.util.OutputTag
import org.example.model.SatellitePositionData
import org.example.model.SatelliteShipData
import org.example.util.CommonUtil.localDateTimeFormatter
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

fun main() {
    val clickhouseAddress = "************:8123"
    val tcpConnections = listOf(
//        NettyClientConfig("*************", 9899, "TjBeiBao", "")
        NettyClientConfig("*************", 9899, "TjBeiBao", "")
    )

    val env = StreamExecutionEnvironment.getExecutionEnvironment()
    val nettySource = NettyTcpSource(tcpConnections)

    val stream = env.fromSource(nettySource, WatermarkStrategy.noWatermarks(), "netty-tcp-source-track")

    // 定义侧输出标签
    val positionOutput = object : OutputTag<SatellitePositionData>("type-position") {}
    val shipOutput = object : OutputTag<SatelliteShipData>("type-ship") {}

    val processedStream = stream.process(object : ProcessFunction<String, List<String>>() {
        override fun processElement(
            value: String,
            ctx: ProcessFunction<String, List<String>>.Context,
            out: org.apache.flink.util.Collector<List<String>>
        ) {
            val parts = value.split("~")
            when (parts[0]) {
                // dtype~CommType~ID~ReceiveTime~PosTime~Lon~Lat~Cog~Sog~TrueHeading~NavigationStatus~ROT~SourceId~PosAccuracy
                "1" -> ctx.output(positionOutput, SatellitePositionData().apply {
                    date = localDateTimeFormatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(parts.elementAtOrNull(4)?.toLongOrNull() ?: 0L), ZoneId.systemDefault()))
                    mmsi = parts.elementAtOrNull(2) ?: ""
                    code = parts.elementAtOrNull(10)?.toIntOrNull() ?: 0
                    turn = parts.elementAtOrNull(11)?.toIntOrNull() ?: 0
                    speed = (parts.elementAtOrNull(8)?.toDoubleOrNull() ?: 0.0) * 0.1
                    accuracy = parts.elementAtOrNull(13)?.toIntOrNull() ?: 0
                    lat = (parts.elementAtOrNull(6)?.toDoubleOrNull() ?: 0.0) / 600000.0
                    lng = (parts.elementAtOrNull(5)?.toDoubleOrNull() ?: 0.0) / 600000.0
                    geohash = GeoHash.withCharacterPrecision(lat, lng, 7).toBase32()
                    course = (parts.elementAtOrNull(7)?.toDoubleOrNull() ?: 0.0) * 0.1
                    heading = parts.elementAtOrNull(9)?.toIntOrNull() ?: 0
                })
                // dtype~CommType~ID~ReceiveTime~IMO~Callsign~ShipName~ShipType~Length~Breadth~ETA~Draught~Destination~SourceId~LengthBow~LengthStern~BreadthPort~BreadthStarboard
                "2" -> ctx.output(shipOutput, SatelliteShipData().apply {
                    date = localDateTimeFormatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(parts.elementAtOrNull(3)?.toLongOrNull() ?: 0L), ZoneId.systemDefault()))
                    mmsi = parts.elementAtOrNull(2) ?: ""
                    imo = parts.elementAtOrNull(4) ?: ""
                    callSign = parts.elementAtOrNull(5) ?: ""
                    name = parts.elementAtOrNull(6) ?: ""
                    type = parts.elementAtOrNull(7)?.toIntOrNull() ?: 0
                    loa = parts.elementAtOrNull(8)?.toIntOrNull() ?: 0
                    bm = parts.elementAtOrNull(9)?.toIntOrNull() ?: 0
                    eta = parts.elementAtOrNull(10) ?: ""
                    draught = (parts.elementAtOrNull(11)?.toDoubleOrNull() ?: 0.0) * 0.1
                    dest = parts.elementAtOrNull(12) ?: ""
                    lengthBow = parts.elementAtOrNull(14)?.toIntOrNull() ?: 0
                    lengthStern = parts.elementAtOrNull(15)?.toIntOrNull() ?: 0
                    breadthPort = parts.elementAtOrNull(16)?.toIntOrNull() ?: 0
                    breadthStarboard = parts.elementAtOrNull(17)?.toIntOrNull() ?: 0
                })
                else -> {}
            }
        }
    })

    // 取出各类型的数据流
    val positionStream = processedStream.getSideOutput(positionOutput)
    val shipStream = processedStream.getSideOutput(shipOutput)

    // stream.sinkTo(sink2)

    Runtime.getRuntime().addShutdownHook(Thread {
        NettyConnectionManager.shutdown()
    })

    env.execute()
}