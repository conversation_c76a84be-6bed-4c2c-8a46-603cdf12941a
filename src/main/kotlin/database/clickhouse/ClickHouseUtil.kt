package org.example.database.clickhouse

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.reflect.full.memberProperties

object ClickHouseUtil {
    suspend fun insertList(list: List<Any>, table: String) = withContext(Dispatchers.IO) {
        require(list.isNotEmpty() && table.isNotEmpty()) { "List and table name cannot be empty" }

        val columns = executeQuery("DESCRIBE TABLE ${table};").map { it.getOrDefault("name", "") }

        batchInsert("""
            INSERT INTO default.${table} VALUES(${columns.joinToString(",") { "?" }});
        """.trimIndent(), list, columns)
    }

    fun executeQuery(sql: String, params: List<Any> = emptyList()): List<Map<String, String>> {
        val result = mutableListOf<Map<String, String>>()
        ClickHouseDataSource.getConnection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                params.forEachIndexed { index, value ->
                    statement.setObject(index + 1, value)
                }
                statement.executeQuery().use { resultSet ->
                    val metaData = resultSet.metaData
                    val columnCount = metaData.columnCount
                    while (resultSet.next()) {
                        val row = mutableMapOf<String, String>()
                        for (i in 1..columnCount) {
                            row[metaData.getColumnName(i)] = resultSet.getString(i)
                        }
                        result.add(row)
                    }
                }
            }
        }

        return result
    }

    private fun batchInsert(sql: String, list: List<Any>, columns: List<String>) {
        ClickHouseDataSource.getConnection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                list.forEach { item ->
                    val properties = item::class.memberProperties
                    columns.forEachIndexed { index, column ->
                        properties.find { it.name == column }?.let { property ->
                            val value = property.getter.call(item)
                            if (value != null) {
                                when (value) {
                                    is String -> statement.setString(index + 1, value)
                                    is Int -> statement.setInt(index + 1, value)
                                    is Float -> statement.setFloat(index + 1, value)
                                    is Double -> statement.setFloat(index + 1, value.toFloat())
                                    is Long -> statement.setLong(index + 1, value)
                                    is Date -> statement.setString(index + 1, SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value))
                                    else -> statement.setString(index + 1, value.toString())
                                }
                            } else {
                                return@forEach
                            }
                        }
                    }
                    statement.addBatch()
                }

                statement.executeBatch()
            }
        }
    }
}