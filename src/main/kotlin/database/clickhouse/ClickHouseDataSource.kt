package org.example.database.clickhouse

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import java.sql.Connection

object ClickHouseDataSource {
    private val hikariConfig = HikariConfig().apply {
        jdbcUrl = "****************************************************************"
        driverClassName = "com.clickhouse.jdbc.ClickHouseDriver"
        username = "default"
        password = "123456" // 如果无密码可留空
        maximumPoolSize = 10
        minimumIdle = 2
        connectionTimeout = 30000
        idleTimeout = 60000
        maxLifetime = 1800000
    }

    val dataSource = HikariDataSource(hikariConfig)

    fun getConnection(): Connection = dataSource.connection
}