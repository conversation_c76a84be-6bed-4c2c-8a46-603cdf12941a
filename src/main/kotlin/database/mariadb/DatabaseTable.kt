package org.example.database.mariadb

import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table


/**
 * -- datacenter.tbl_stationmonitor definition
 *
 * CREATE TABLE `tbl_stationmonitor` (
 *   `id` varchar(32) NOT NULL,
 *   `stationName` varchar(100) DEFAULT NULL,
 *   `ip` varchar(100) DEFAULT NULL,
 *   `port` int(11) DEFAULT NULL,
 *   `state` tinyint(4) DEFAULT NULL,
 *   `latestReportTime` datetime DEFAULT NULL,
 *   `fromType` varchar(100) DEFAULT NULL,
 *   `sysCreated` timestamp NOT NULL DEFAULT current_timestamp(),
 *   `sysUpdated` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 *   `sysDeleted` tinyint(1) DEFAULT NULL,
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
 */
object StationMonitors : Table("tbl_stationmonitor") {
    val id: Column<String> = varchar("id", 32)
    val stationName: Column<String> = varchar("stationName", 100)
    val ip: Column<String> = varchar("ip", 100)
    val port: Column<Int> = integer("port")
    val state: Column<Int> = integer("state")
    val latestReportTime: Column<String> = varchar("latestReportTime", 100)
    val fromType: Column<String> = varchar("fromType", 100)
    val sysCreated: Column<String> = varchar("sysCreated", 100)
    val sysUpdated: Column<String> = varchar("sysUpdated", 100)
    val sysDeleted: Column<Int> = integer("sysDeleted")
    override val primaryKey = PrimaryKey(id, name = "PK_StationMonitor")
}