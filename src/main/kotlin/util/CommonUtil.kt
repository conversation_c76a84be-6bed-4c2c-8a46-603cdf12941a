package org.example.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object CommonUtil {
    val localDateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val localDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")

    val Any.log: Logger
        get() = LoggerFactory.getLogger(this.javaClass)
}

val customObjectMapper = ObjectMapper()

fun hexToBytes(hex: String): ByteArray {
    val result = ByteArray(hex.length / 2)
    for (i in hex.indices step 2) {
        result[i / 2] = ((hex[i].digitToInt(16) shl 4) + hex[i + 1].digitToInt(16)).toByte()
    }
    return result
}

fun aesDecrypt(data: String, key: String, iv: String): String? {
    return try {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val keyBytes = hexToBytes(key)
        val ivBytes = hexToBytes(iv)
        val secretKey = SecretKeySpec(keyBytes, "AES")
        val ivSpec = IvParameterSpec(ivBytes)

        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
        val decodedBytes = Base64.getDecoder().decode(data.split(" ", limit = 3).last())
        val decryptedBytes = cipher.doFinal(decodedBytes)
        String(decryptedBytes, Charsets.UTF_8)
    } catch (e: Exception) {
        println("$data $iv")
        null
    }
}
