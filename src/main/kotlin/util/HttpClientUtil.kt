package org.example.util

import io.ktor.client.*
import io.ktor.client.call.body
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.contentType
import io.ktor.http.isSuccess
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.example.datasource.msa.guangdong.GDTask.URL
import org.example.datasource.msa.guangdong.GDTask.USERNAME
import org.example.datasource.msa.guangdong.GDTask.endpointMap
import org.example.datasource.msa.guangdong.LoginRequest
import org.example.http.HttpSplitConfig
import org.example.util.CommonUtil.log
import org.example.util.HttpClientUtil.client
import org.example.util.HttpClientUtil.retryRequest

object HttpClientUtil {
    const val MAX_RETRIES = 3
    val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
            })
        }
    }

    suspend fun <T> retryRequest(request: suspend () -> T): T {
        var retries = 0
        while (true) {
            try {
                return request()
            } catch (e: Exception) {
                retries++
                if (retries >= MAX_RETRIES) {
                    log.error("$retries retries exceeded, ${e.message}")
                    throw e
                }
            }
        }
    }
}

fun main() = runBlocking {
//    val response = retryRequest<ResponseTest> {
//        client.get("http://192.168.1.19:4217/api/Draw/getAll").body()
//    }
//    println(response.info)
    println(LoginRequest("MSA", "", "").toString())
}