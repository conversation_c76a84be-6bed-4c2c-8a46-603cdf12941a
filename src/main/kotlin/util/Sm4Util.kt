package org.example.util

import org.bouncycastle.jce.provider.BouncyCastleProvider
import java.security.Key
import java.security.Security
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object Sm4Util {
    const val ENCODING = "UTF-8"
    const val ALGORITHM_NAME = "SM4"
    const val ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding"
    const val DEFAULT_KEY_SIZE = 128
    const val PROVIDER_NAME = "BC"

    @Throws(Exception::class)
    fun encryptEcb(hexKey: String?, paramStr: String): String? {
        var cipherText: String? = ""
        // 16进制字符串-->byte[]
        val keyData: ByteArray = fromHexString(hexKey ?: "")
        // String-->byte[]
        val srcData = paramStr.toByteArray(charset(ENCODING))
        // 加密后的数组
        val cipherArray: ByteArray = encryptEcbPadding(keyData, srcData)
        // byte[]-->hexString
        cipherText = toHexString(cipherArray)
        return cipherText
    }

    @Throws(java.lang.Exception::class)
    fun encryptEcbPadding(key: ByteArray, data: ByteArray): ByteArray {
        val cipher: Cipher = generateEcbCipher(key) //声称Ecb暗号,通过第二个参数判断加密还是解密
        return cipher.doFinal(data)
    }

    @Throws(java.lang.Exception::class)
    private fun generateEcbCipher(key: ByteArray): Cipher {
        val cipher: Cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, BouncyCastleProvider.PROVIDER_NAME)
        val sm4Key: Key = SecretKeySpec(key, ALGORITHM_NAME)
        cipher.init(Cipher.ENCRYPT_MODE, sm4Key)
        return cipher
    }

    fun fromHexString(hex: String): ByteArray {
        return hex.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
    }

    fun toHexString(bytes: ByteArray): String {
        return bytes.joinToString("") { "%02x".format(it) }
    }

    fun registerBouncyCastleProvider() {
        // 避免重复注册
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }
    }
}