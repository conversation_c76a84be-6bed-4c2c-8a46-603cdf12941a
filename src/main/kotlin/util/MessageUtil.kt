package org.example.util

import org.example.annotations.Bit
import org.example.annotations.Order
import org.example.annotations.UniqueId
import org.example.model.ais.AisMessage
import org.example.model.ais.*
import org.example.model.ais.NMEA
import org.example.util.CommonUtil.log
import org.reflections.Reflections
import kotlin.collections.set
import kotlin.collections.sortedBy
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

data class AisMessageProperty(
    val clazz: Class<*>,
    val bitProperties: List<BitProperty>
)

data class BitProperty(
    val returnType: Int,
    val bit: Bit,
    val property: KMutableProperty<*>
)

object MessageUtil {
    private var payload = StringBuilder()
    private val fastReader = SixBitWriter(2048)
    val uniqueIdClassMap = mutableMapOf<Int, Class<*>>()
    val nmeaProperties = NMEA()::class.memberProperties.associate {
        it.isAccessible = true
        (it.javaField?.getAnnotation(Order::class.java)?.index ?: 0) to (it as KMutableProperty<*>)
    }
    val charToSixBitMap = mutableMapOf<Char, String>()

    init {
        for (i in 0..127) {
            val char = i.toChar()
            charToSixBitMap[char] = charToSixBitStr(char)
        }
    }

    fun decodeSingleMessage(message: String): AisMessage? {
        val binaryData = payloadToSixBit(preprocessMessage(message).filter { it.header.endsWith("VDM") || it.header.endsWith("VDO") }.joinToString("") { it.payload })
        val messageId = binaryData.take(6).toInt(2)
        return uniqueIdClassMap[messageId]?.getConstructor(String::class.java)?.newInstance(binaryData) as AisMessage?
    }

    fun generateOutputString(any: Any): String {
        val properties = any::class.memberProperties.sortedBy { it.javaField?.getAnnotation(Bit::class.java)?.index ?: 0 }.onEach { it.isAccessible = true }
        return properties.joinToString("\n") { property ->
            val annotation = property.javaField?.getAnnotation(Bit::class.java) ?: return@joinToString ""
            when (property.returnType.classifier) {
                Int::class, Long::class, String::class -> "${annotation.description}=${property.getter.call(any)}"
                else -> property.getter.call(any)?.let { generateOutputString(it) } ?: "未知字段=${property.getter.call(any)}"
            }
        }
    }

    fun getMessageId(): Int {
        return fastReader.getFirstSixBits()
    }

    fun getAisMessage3(): AisMessage {
        val msgId = getMessageId()
        return when (msgId) {
            1 -> AisMessage1(toByteArray())
            2 -> AisMessage2(toByteArray())
            3 -> AisMessage3(toByteArray())
            4 -> AisMessage4(toByteArray())
            5 -> AisMessage5(toByteArray())
            6 -> AisMessage6(toByteArray())
            7 -> AisMessage7(toByteArray())
            8 -> AisMessage8(toByteArray())
            9 -> AisMessage9(toByteArray())
            10 -> AisMessage10(toByteArray())
            11 -> AisMessage11(toByteArray())
            12 -> AisMessage12(toByteArray())
            13 -> AisMessage13(toByteArray())
            14 -> AisMessage14(toByteArray())
            15 -> AisMessage15(toByteArray())
            16 -> AisMessage16(toByteArray())
            17 -> AisMessage17(toByteArray())
            18 -> AisMessage18(toByteArray())
            19 -> AisMessage19(toByteArray())
            21 -> AisMessage21(toByteArray())
            22 -> AisMessage22(toByteArray())
            23 -> AisMessage23(toByteArray())
            24 -> AisMessage24(toByteArray())
            25 -> AisMessage25(toByteArray())
            26 -> AisMessage26(toByteArray())
            27 -> AisMessage27(toByteArray())
            else -> throw IllegalArgumentException("Unsupported AIS message id: $msgId")
        }
    }

    /**
     *
     * this method is used to preprocess the AIS message before decoding.
     * input: !AIVDM,2,1,9,B,569FS3T000009HIWF20P4V1PT4pN0PtpN2222216=8E9:5`bNDB0BH2k,0*01!AIVDM,2,2,9,B,mH8888888888880,2*0B$AIVSI,TEST BS 360,9,015350.856770,1907,-105,24*78
     * result:
     * !AIVDM,2,1,9,B,569FS3T000009HIWF20P4V1PT4pN0PtpN2222216=8E9:5`bNDB0BH2k,0*01
     * !AIVDM,2,2,9,B,mH8888888888880,2*0B
     * !AIVSI,TEST BS 360,9,015350.856770,1907,-105,24*78
     */
    fun preprocessMessage(message: String): MutableList<NMEA> {
        val list = mutableListOf<NMEA>()
        message.split("\n").filter { it.isNotEmpty() }.forEach {
            creatNMEA(list, it)
        }
        return list
    }

    fun toByteArray(): ByteArray {
        return fastReader.toByteArray()
    }

    fun readSingleLine(message: String) {
        fastReader.reset()
        var start = 0

        while (start < message.length) {
            val char = message[start]
            when (char) {
                '!', '\$' -> {
                    val nmea = NMEA()
                    var propertyIndex = 0
                    var next = 0

                    try {
                        while (true) {
                            if (propertyIndex >= 6) {
                                val next = message.indexOf('*', start)
                                nmea.padBits = message.substring(start, next)
                                nmea.checksum = message.substring(next + 1, next + 3)
                                start = next + 2
                                if (nmea.header.endsWithVmdOrVdoFast()) {
                                    nmea.payload.forEach { char ->
                                        fastReader.writeBits(charToSixBit(char))
                                    }
                                    if (nmea.padBits.isNotEmpty()) {
                                        fastReader.writeBits(0, nmea.padBits.toInt())
                                    }
                                }
                                break
                            }

                            next = message.indexOf(',', start)

                            val property = nmeaProperties[propertyIndex] ?: break

                            when (next - start) {
                                0 -> continue
                                1 -> property.setter.call(nmea, message[start].toString())
                                else -> property.setter.call(nmea, message.substring(start, next))
                            }

                            start = next
                            do {
                                start ++
                                propertyIndex ++
                            } while (message[start] == ',')
                        }
                    } catch (e: Exception) {
                        println("$message, ${e.message}")
                    }

                }
            }
            start ++
        }
    }

    fun String.endsWithVmdOrVdoFast(): Boolean {
        if (this.length < 3) return false
        val suffix = this.takeLast(3).uppercase()
        return suffix == "VDM" || suffix == "VDO"
    }

    fun getAisMessage2(): AisMessage {
        val byteArray = fastReader.toByteArray()
        val msgId = BitReader(byteArray).readBits(6)

        return when (msgId) {
            1 -> AisMessage1(byteArray)
            2 -> AisMessage2(byteArray)
            3 -> AisMessage3(byteArray)
            4 -> AisMessage4(byteArray)
            5 -> AisMessage5(byteArray)
            6 -> AisMessage6(byteArray)
            7 -> AisMessage7(byteArray)
            8 -> AisMessage8(byteArray)
            9 -> AisMessage9(byteArray)
            10 -> AisMessage10(byteArray)
            11 -> AisMessage11(byteArray)
            12 -> AisMessage12(byteArray)
            13 -> AisMessage13(byteArray)
            14 -> AisMessage14(byteArray)
            15 -> AisMessage15(byteArray)
            16 -> AisMessage16(byteArray)
            17 -> AisMessage17(byteArray)
            18 -> AisMessage18(byteArray)
            19 -> AisMessage19(byteArray)
            21 -> AisMessage21(byteArray)
            22 -> AisMessage22(byteArray)
            23 -> AisMessage23(byteArray)
            24 -> AisMessage24(byteArray)
            25 -> AisMessage25(byteArray)
            26 -> AisMessage26(byteArray)
            27 -> AisMessage27(byteArray)
            else -> throw IllegalArgumentException("Unsupported AIS message id: $msgId")
        }
    }

    fun creatNMEA(message: String) {
        val nmea = NMEA()
        var start = 0
        while (start < message.length) {
            val char = message[start]
            when (char) {
                '!', '\$' -> {
                    var propertyIndex = 0
                    var next = 0
                    try {
                        while (true) {
                            if (propertyIndex >= 6) {
                                val next = message.indexOf('*', start)
                                nmea.padBits = message.substring(start, next)
                                nmea.checksum = message.substring(next + 1, next + 3)
                                start = next + 2
                                if (nmea.header.endsWithVmdOrVdoFast()) {
                                    nmea.payload.forEach { char ->
                                        fastReader.writeBits(charToSixBit(char))
                                    }
                                    if (nmea.padBits.isNotEmpty()) {
                                        fastReader.writeBits(0, nmea.padBits.toInt())
                                    }
                                }
                                break
                            }
                            next = message.indexOf(',', start)
                            val property = nmeaProperties[propertyIndex] ?: break
                            when (next - start) {
                                0 -> continue
                                1 -> property.setter.call(nmea, message[start].toString())
                                else -> property.setter.call(nmea, message.substring(start, next))
                            }

                            start = next
                            do {
                                start ++
                                propertyIndex ++
                            } while (message[start] == ',')
                        }
                    } catch (e: Exception) {
                        log.error("creat nmea error $message, ${e.message}")
                    }

                }
            }
            start ++
        }
    }

    fun charToSixBitStr(char: Char): String {
        val asciiValue = char.code
        return if (asciiValue < 96) {
            (asciiValue - 48).toString(2).padStart(6, '0')
        } else {
            (asciiValue - 56).toString(2).padStart(6, '0')
        }
    }

    fun charToSixBit(char: Char): Int {
        require(char.code in 48..119) { "Invalid character: $char" }

        val asciiValue = char.code
        return if (asciiValue < 96) {
            asciiValue - 48
        } else {
            asciiValue - 56
        }
    }

    fun charToSixBit2(char: Char): String {
        val asciiValue = char.code
        return if (asciiValue < 88) {
            (asciiValue - 48).toString(2).padStart(6, '0')
        } else {
            (asciiValue - 56).toString(2).padStart(6, '0')
        }
    }

    fun payloadToSixBit(payload: String): String {
//        return payload.map { charToSixBit(it) }.joinToString("")
        return payload.map { charToSixBitMap[it] }.joinToString("")
    }

    fun reset() {
        fastReader.reset()
    }
}

fun main() {
//    MessageUtil.readSingleLine("!ABVDM,1,1,,A,16:<lJP01i8KG9hF;avbQpEl04Kd,0*7C").forEach {
//        println(jacksonObjectMapper.writeValueAsString(it))
//    }
    MessageUtil.readSingleLine("!AIVDM,2,1,4,A,569USrP0000094e@001PT60L4pN1ADv3;@00000l2PD552jlN;Dj0CUiB@00,0*30!AIVDM,2,2,4,A,00000000004,2*1B")
}
