package org.example.util

class BitReader(private val data: ByteArray) {
    private var byteIdx = 0      // 当前所处字节
    private var bitOffset = 0    // 0 = 高位，7 = 低位

    /** 读取 n (1‒64) 个比特，返回低 n 位有效的 Long */
    fun readBits(n: Int): Int {
//        require(n in 1..64) { "n must be 1..64" }

        return try {
            var remaining = n
            var value = 0

            while (remaining > 0) {
                val bitsLeftInByte = 8 - bitOffset
                val bitsToRead = minOf(remaining, bitsLeftInByte)

                val currentByte = data[byteIdx].toInt() and 0xFF
                val shift = bitsLeftInByte - bitsToRead
                val mask = (1 shl bitsToRead) - 1

                val bits = (currentByte shr shift) and mask
                value = (value shl bitsToRead) or bits

                bitOffset += bitsToRead
                if (bitOffset == 8) {              // 下一个字节
                    bitOffset = 0
                    byteIdx++
                }
                remaining -= bitsToRead
            }

            value
        } catch (e: Exception) {
            0
        }
    }

//    fun readBitsToInt(n: Int): Int {
//
//    }

    fun readBitsToByteArray(bitCount: Int): ByteArray {
        val byteCount = (bitCount + 7) / 8
        val result = ByteArray(byteCount)
        var bitsWritten = 0

        for (i in 0 until byteCount) {
            val bitsLeft = bitCount - bitsWritten
            val bitsThisByte = minOf(8, bitsLeft)
            val value = readBits(bitsThisByte).toInt() and ((1 shl bitsThisByte) - 1)
            result[i] = (value shl (8 - bitsThisByte)).toByte()  // 左对齐该字节
            bitsWritten += bitsThisByte
        }
        return result
    }
}

class SixBitWriter(maxSize: Int) {
    private var bitOffset = 0
    private var currentByte = 0
    private var writeIndex = 0
    private var buffer = ByteArray(maxSize)

    fun writeBits(inputBits: Int, bitCount: Int = 6) {
        var remainingBits = bitCount
        var bitsToWrite = inputBits

        while (remainingBits > 0) {
            val spaceInCurrentByte = 8 - bitOffset

            if (remainingBits >= spaceInCurrentByte) {
                val result = (currentByte or (bitsToWrite shr (remainingBits - spaceInCurrentByte))).toByte()
                buffer[writeIndex++] = result

                remainingBits -= spaceInCurrentByte
                bitsToWrite = bitsToWrite and ((1 shl remainingBits) - 1)
                bitOffset = 0
                currentByte = 0
            } else {
                currentByte = currentByte or (bitsToWrite shl (spaceInCurrentByte - remainingBits))

                bitOffset += remainingBits
                remainingBits = 0
            }
        }
    }

    /**
     * 获取当前写入的所有字节。
     * 如果最后一个字节未满 8 比特，剩余部分将用 0 填充。
     */
    fun toByteArray(): ByteArray {
        // 如果 currentByte 中还有未写入的比特，将其写入最后一个字节
        if (bitOffset > 0) {
            ensureCapacity(writeIndex + 1)
            buffer[writeIndex++] = currentByte.toByte()
        }
        return buffer.copyOf(writeIndex)
    }

    fun reset() {
        bitOffset = 0
        currentByte = 0
        writeIndex = 0
    }

    /**
     * 确保内部缓冲数组有足够的容量。
     */
    private fun ensureCapacity(minCapacity: Int) {
        if (minCapacity > buffer.size) {
            val newCapacity = (buffer.size * 2).coerceAtLeast(minCapacity)
            buffer = buffer.copyOf(newCapacity)
        }
    }

    fun getFirstSixBits(): Int {
        require(buffer.isNotEmpty()) { "Buffer is empty" }
        return (buffer[0].toInt() shr 2)
    }
}
