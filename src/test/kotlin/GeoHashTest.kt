import ch.hsr.geohash.GeoHash
import org.example.util.aesDecrypt
import org.junit.jupiter.api.Test
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class GeoHashTest {
    @Test
    fun test1() {
        val key = "A83E09D85D5E4C3A7DA4E9764A5785C0"
        val str = "d 1 cPXPFh89fcQGeUxfM+MhRWwlZZsH9xImo2QGZyv+hgMSXCDl7nwFsgRbW9i3DtO7K7YzS7VzBHyWRouny0KVOhsmT9sPagfL/ltYAP9Uz4I="
        val data = str.split(" ", limit = 3).last()
        println(data)
        println(decrypt(data, "3f8e7b2c5d1a4f6e9b0c2d3e4f5a6b7c", key))
    //        println(GeoHash.withCharacterPrecision(22.7391891479, 116.006149292, 7).toBase32())
    }

    fun hexToBytes(hex: String): ByteArray {
        val result = ByteArray(hex.length / 2)
        for (i in hex.indices step 2) {
            result[i / 2] = ((hex[i].digitToInt(16) shl 4) + hex[i + 1].digitToInt(16)).toByte()
        }
        return result
    }

    fun decrypt(data: String, key: String, iv: String): String? {
        return try {
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            val keyBytes = hexToBytes(key)
            val ivBytes = hexToBytes(iv)
            val secretKey = SecretKeySpec(keyBytes, "AES")
            val ivSpec = IvParameterSpec(ivBytes)

            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
            val decodedBytes = Base64.getDecoder().decode(data)
            val decryptedBytes = cipher.doFinal(decodedBytes)
            String(decryptedBytes, Charsets.UTF_8)
        } catch (e: Exception) {
            println("Decryption error: ${e.message}")
            null
        }
    }
}