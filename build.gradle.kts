plugins {
    kotlin("jvm") version "2.1.20"
    kotlin("plugin.serialization") version "2.1.20" // 添加序列化插件
    kotlin("kapt") version "2.1.20"
    application
}

group = "org.example"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

val ktorVersion = "3.0.2"

dependencies {
    implementation(kotlin("stdlib"))
    implementation(kotlin("reflect"))

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")

    implementation("io.ktor:ktor-server-core:${ktorVersion}")
    implementation("io.ktor:ktor-server-netty:${ktorVersion}")
    implementation("io.ktor:ktor-server-content-negotiation:${ktorVersion}")
    implementation("io.ktor:ktor-serialization-kotlinx-json:${ktorVersion}")
    implementation("io.ktor:ktor-server-call-logging:${ktorVersion}")
    implementation("io.ktor:ktor-client-core:${ktorVersion}")
    implementation("io.ktor:ktor-client-cio:${ktorVersion}")
    // CIO引擎，支持并发、非阻塞
    implementation("io.ktor:ktor-client-content-negotiation:${ktorVersion}")
    implementation("io.ktor:ktor-serialization-kotlinx-json:${ktorVersion}")

    implementation("io.netty:netty-all:4.1.107.Final")

    implementation("org.jetbrains.exposed:exposed-core:0.45.0")
    implementation("org.jetbrains.exposed:exposed-dao:0.45.0")
    implementation("org.jetbrains.exposed:exposed-jdbc:0.45.0")
    implementation("org.jetbrains.exposed:exposed-java-time:0.45.0")

    implementation("com.clickhouse:clickhouse-jdbc:0.8.2:shaded-all")
    implementation("org.jetbrains.kotlinx:dataframe-jdbc:0.13.0")
    implementation("com.zaxxer:HikariCP:5.1.0")
    implementation("org.mariadb.jdbc:mariadb-java-client:3.3.3")

    implementation("ch.qos.logback:logback-classic:1.4.14")
    implementation("org.slf4j:slf4j-api:2.0.13")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.1")
    implementation("org.bouncycastle:bcprov-jdk15to18:1.78")

    testImplementation("org.junit.jupiter:junit-jupiter:5.10.2")

    // Flink core API
    implementation("org.apache.flink:flink-java:1.18.1")
    implementation("org.apache.flink:flink-streaming-java:1.18.1")
    implementation("org.apache.flink:flink-clients:1.18.1")
    implementation("org.apache.flink:flink-connector-files:1.18.1")
    implementation("org.apache.flink:flink-connector-jdbc:3.1.2-1.18")

    implementation("com.github.ben-manes.caffeine:caffeine:3.1.8")

    implementation("org.mapstruct:mapstruct:1.5.5.Final")
    kapt("org.mapstruct:mapstruct-processor:1.5.5.Final")

    implementation("org.apache.kafka:kafka-clients:3.9.1") // Kafka 最新版本

    implementation("ch.hsr:geohash:1.4.0")
    implementation("org.reflections:reflections:0.10.2")
}

tasks.test {
    useJUnitPlatform()
}
kotlin {
    jvmToolchain(17)
}

application {
    mainClass.set("org.example.MainKt") // 注意：Main.kt 对应的 class 是 MainKt
}

tasks.jar {
    manifest {
        attributes(
            "Main-Class" to "org.example.markais.MarkAisKt",
            "Class-Path" to configurations.runtimeClasspath.get()
                .joinToString(" ") { "lib/${it.name}" }
        )
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}


tasks.register<Copy>("copyLibs") {
    from(configurations.runtimeClasspath)
    into(layout.buildDirectory.dir("libs/lib"))
}

tasks.build {
    dependsOn("copyLibs")
}